<?php

namespace <PERSON><PERSON>\LP\Console\Commands\Polls;

use DI\Attribute\Inject;
use <PERSON>no\LP\App\Polls\Models\PollQuery;
use <PERSON>no\LP\App\Polls\PollsController;
use Lamano\LP\Console\Commands\ManagedCommand;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'polls:start',
    description: 'Starts polls according to their schedules.',
//    aliases: ['startAndStopPolls'],
    hidden: false
)]
class StartScheduledPolls extends ManagedCommand
{
    #[Inject]
    protected PollsController $pollsController;

    protected function configure(): void
    {
        $this->addOption(
            'limit',
            null,
            InputOption::VALUE_OPTIONAL,
            'Limit the number of polls to start'
        );
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): int
    {
        $options = $this->validate(
            [
            'limit' => is()->integer()
            ]
        );

        $limit = $options['limit'] ?? -1;
        $polls = PollQuery::findStartablePolls($limit);
        foreach ($polls as $poll) {
            $this->pollsController->startPoll($poll);
            $output->writeln('started poll: ' . $poll->getId());
        }

        return Command::SUCCESS;
    }
}
