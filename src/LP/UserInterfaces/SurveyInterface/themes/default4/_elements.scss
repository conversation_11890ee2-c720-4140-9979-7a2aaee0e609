@use "sass:math";
@use "../mixins";

$checksize: 1.4em;
$slider_handle_size: 1.8em;

.default4 {
  *:focus-visible {
    outline-width: 2px;
    outline-style: solid;
    outline-offset: 2px;
    opacity: 1;
  }

  .modern-image {
    input[type=checkbox], input[type=radio] {
      visibility: hidden;
      position: absolute;

      &:checked + label .check-mark {
        opacity: 1;
      }
    }

    &:hover input[type=checkbox]:not([disabled]),
    &:hover input[type=radio]:not([disabled]) {
      & + label .check-mark {
        opacity: 0.60;
      }

      &:checked + label .check-mark {
        opacity: 1;
      }
    }

    label {
      display: block;
      cursor: pointer;
      text-align: center;
      position: relative;
      overflow: hidden;

      img {
        vertical-align: middle;
      }

      .check-mark {
        $chksz: 80px;
        opacity: 0;
        position: absolute;
        width: $chksz*2;
        left: -($chksz);
        padding-top: $chksz;
        margin-top: math.div($chksz, -2);
        display: block;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        pointer-events: none;
        text-align: center;
        content: '';

        transform: rotate(-45deg);

        .check-mark-icon {
          position: absolute;
          bottom: 2px;
          left: 65px;
          transform: rotate(45deg);
          font-size: 20px;
        }
      }
    }
  }

  #lp-preview .modern-image .check-mark-icon {
    bottom: 0.1em;
  }

  .modern-checkbox {
    position: relative;
    min-height: #{($checksize)};

    input[type=checkbox], input[type=radio] {
      visibility: hidden;
      position: absolute;

      &:checked + label:before {

      }

      &:checked + label:after {
        opacity: 1;
      }
    }

    &:hover input[type=checkbox]:not([disabled]),
    &:hover input[type=radio]:not([disabled]) {
      + label:before,
      &:checked + label:before {
      }

      + label:after {
        opacity: 0.40;
      }

      &:checked + label:after {
        opacity: 1;
      }
    }

    label {
      cursor: pointer;
      padding-left: #{($checksize)+0.3};
      padding-right: #{($checksize)+0.3};
      line-height: #{($checksize)*1.05};
      display: inline-block;
      width: 100%;
      vertical-align: top;

      &:empty {
        width: $checksize;
        height: $checksize;
        position: relative;
        padding: 0;

        &:before {
          display: block;
        }
      }

      &:before {
        position: absolute;
        width: $checksize;
        height: $checksize;
        top: 0;
        left: 0;
        border-width: 1px;
        border-style: solid;
        content: '';
      }

      &:after {
        position: absolute;
        opacity: 0;
        text-align: center;
        width: $checksize;
        line-height: $checksize;
        vertical-align: middle;
        font-family: fontello;
        color: white;
        content: '\e940';
        font-size: 0.85em;
        top: 0.125em;
        left: 0.125em;
      }

      img {
        max-width: 100%;
        height: auto !important;
      }
    }

    input[disabled] + label {
      cursor: default;

      &:before {
        border-color: #777777;
        background-color: #D7D7D7;
        box-shadow: none;
      }
    }
  }

  .modern-radio {
    @extend .modern-checkbox;

    label {
      &:before {
        border-radius: 50% !important;
      }

      &:after {
        top: 0.15em;
        left: 0.1em;
      }
    }
  }

  .modern-select {
    select {
      border-width: 1px;
      border-style: solid;
      padding: 0.4em 0.4em;
      width: auto;
      max-width: 100%;
      cursor: pointer;
    }

  }
}

.default4 {
  .corner {
    .poll-msg-box {
      border-radius: 0.25em;
    }
  }

  .poll-msg-box {
    border-width: 1px;
    border-style: solid;
    font-size: .9em;
    padding: 0.25em 1.2em;
    position: relative;
  }

  .corner {
    .button:not(.ui) {
      border-radius: 0.25em;
    }
  }

  .button:not(.ui) {
    -webkit-appearance: none;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    padding: 0.75em 1.25em;
    margin: 0;
    border: 1px solid transparent;
    text-decoration: none;
    letter-spacing: 0.075em;
    display: inline-block;
    white-space: nowrap;

    border-radius: 0;
    box-shadow: none;
    text-shadow: none;

    &:hover {
      @include mixins.hoverStyle
    }

    &.small {
      padding: 0.5em 0.75em;
      font-size: 0.9em;
    }

    &[name=back]:before {
      font-family: fontello;
      content: '\e995';
      padding-right: 0.5em;
      font-size: 0.7em;
    }

    &[name=next]:after {
      font-family: fontello;
      content: '\e994';
      padding-left: 0.5em;
      font-size: 0.7em;
    }
  }

  .flat-round-button {
    display: inline-block;
    text-align: center;
    padding-top: 0.25em;
    border-radius: 50%;
    border: 1px solid transparent;
    width: 2em;
    height: 2em;
    cursor: pointer;
  }

  .corner {
    .spinbox {
      .spinbox-button.spinUp {
        border-top-right-radius: 0.25em;
      }

      .spinbox-button.spinDown {
        border-bottom-right-radius: 0.25em;
      }
    }
  }

  .spinbox {
    position: relative;
    display: block;

    .input-element {
      text-align: right;
      padding-right: 1.15em !important;
    }

    .spinbox-button {
      position: absolute;
      right: 0;
      border-width: 1px;
      border-style: solid;
      height: 50%;
      width: 1em;
      cursor: pointer;
      text-align: center;

      &:active {
        box-shadow: 0 1px 0 rgba(255, 255, 255, .5) inset, 0 -1px 0 rgba(0, 0, 0, .2) inset, 0 5px 5px -5px rgba(255, 255, 255, .5) inset;
      }

      &:before {
        font-family: fontello;
        vertical-align: top;
        text-align: center;
        font-size: 1em;
        line-height: 1em; // sonst bekommt IE ne krise
        display: block;
      }

      &.spinUp {
        &:before {
          content: '\e84f';
        }

        top: 0;
        border-bottom: 0;
      }

      &.spinDown {
        &:before {
          content: '\e850';
        }

        bottom: 0;
        border-top: 0;
      }
    }

  }

  .corner {
    .horizontal-slider, .ui-slider-range-min {
      border-radius: 0.25em;
    }
  }

  .horizontal-slider {
    position: relative;
    border-radius: 0;
    width: 100%;
    height: 1em;
    border-width: 1px;
    border-style: solid;
    padding: 0;
    margin: 0;
    cursor: pointer;

    .ui-slider-range-min {
      display: block;
      height: 100%;
      position: absolute;
      opacity: 0.80;
      padding: 0;
    }

    &[initial] .ui-slider-range-min {
      display: none;
    }

    .ui-slider-handle {
      position: absolute;
      cursor: pointer;
      height: $slider_handle_size;
      width: $slider_handle_size;
      border-radius: 50%;
      margin-left: math.div($slider_handle_size, -2);
      top: math.div($slider_handle_size, -4);
      padding: 0;
      border: 1px solid;
      z-index: 3;

      box-shadow: 0 1px 2px 0 rgba(34, 36, 38, .15), 0 0 0 1px rgba(34, 36, 38, .15) inset;

      &:active, &:focus {
        box-shadow: inset 0 0em 2em rgba(0, 0, 0, 0.25);
      }

      &[initial] {
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }

      .sliderTooltipWrapper {
        display: block;
        position: absolute;
        left: 50%;
        top: -7px;
        z-index: 4;
      }

      .sliderTooltip.bubble.up {
        position: relative;
        display: none;
        margin: auto;
        z-index: 2;
        bottom: auto;
        top: -$slider_handle_size;
        margin-left: -50%;
        font-weight: normal;
      }
    }
  }

  .corner {
    .bubble {
      border-radius: 0.25em;
    }
  }

  .bubble {
    position: relative;
    border-radius: 0;
    text-align: center;
    border: 1px solid transparent;
    font-size: 0.9em;
    padding: .1em .5em;
    display: inline-block;

    img, span, div, p {
      vertical-align: middle;
    }

    &:before, &:after {
      display: block;
      content: '';
      position: absolute;
      width: 0;
      height: 0;
    }

    &:before {
      border: 6px solid transparent;
      z-index: 2;
    }

    &:after {
      border: 7px solid transparent;
      z-index: 1;
    }

    &.up, &.down {
      &:before, &:after {
        left: 50%;
      }

      &:before {
        margin-left: -6px;
      }

      &:after {
        margin-left: -7px;
      }
    }

    &.left, &.right {
      &:before, &:after {
        top: 50%;
      }

      &:before {
        margin-top: -6px;
      }

      &:after {
        margin-top: -7px;
      }
    }

    &.up {
      bottom: 7px;

      &:before, &:after {
        top: 100%;
      }
    }

    &.down {
      top: 7px;

      &:before, &:after {
        bottom: 100%;
      }
    }

    &.left {
      margin-right: 7px;

      &:before, &:after {
        left: 100%;
      }
    }

    &.right {
      margin-left: 7px;

      &:before, &:after {
        right: 100%;
      }
    }
  }

  .ui-datepicker {
    border-width: 1px;
    border-style: solid;
    display: none;
    box-shadow: 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.3);

    &, td, th {
      font-size: 0.75em;
    }

    td {
      text-align: center;
      border-width: 1px;
      border-style: dotted;
      padding: 0.25em;
      cursor: pointer;
    }

    td.ui-datepicker-week-col {
      border: 0;
      cursor: default;
    }

    td.ui-datepicker-current-day {
      border-style: solid;
    }

    a {
      text-decoration: none;
    }

    .ui-datepicker-header {
      text-align: center;

      .ui-datepicker-title, a {
        padding: 0.25em;
        cursor: pointer;
      }

      .ui-datepicker-prev {
        float: left;
      }

      .ui-datepicker-next {
        float: right;
      }
    }

  }

  .spinner {
    white-space: nowrap;

    > div {
      width: 1.4em;
      height: 1.4em;
      margin-right: 0.7em;

      &:last-child {
        margin-right: 0;
      }

      border-radius: 50%;
      display: inline-block;
      animation: bouncedelay 1.4s infinite ease-in-out;
    }

    .bounce1 {
      animation-delay: -0.32s;
    }

    .bounce2 {
      animation-delay: -0.16s;
    }
  }
}

@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
  }
  40% {
    transform: scale(1.0);
  }
}

@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
  }
  40% {
    transform: scale(1.0);
  }
}

