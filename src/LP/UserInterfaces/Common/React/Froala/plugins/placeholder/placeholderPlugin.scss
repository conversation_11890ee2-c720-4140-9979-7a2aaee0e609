#placeholderMenu {
  width: 26rem;
  padding: 0;

  .ui.segment {
    margin: 0;
  }

  .placeholders {
    padding: 0.5em 0;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 20rem;
    @media only screen and (max-width: 767px) {
      max-height: 15rem;
    }


    div {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      max-width: 26rem;
      line-height: 1.2em;
    }

    .ph-title {
      padding: 0;
      font-family: inherit;
    }

    .ph-item {
      padding-left: 1em;
    }

    .subPH-list .ph-item {
      padding-left: 2em;
    }
  }

  .locked-placeholders {
    white-space: normal;
    max-width: 25em;

    ul {
      list-style-type: disc;
      padding-left: 2em;

      li {
        padding: 0.2em 0;
      }
    }
  }
}

//}
