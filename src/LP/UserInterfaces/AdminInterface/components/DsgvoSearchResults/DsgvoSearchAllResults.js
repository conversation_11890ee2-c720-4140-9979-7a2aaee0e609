import React from 'react';
import {Divider, Segment} from 'semantic-ui-react';
import {DsgvoSingleResultPanel} from './DsgvoSingleResultPanel.js';
import {useTranslator} from '../../../AppInterface/lib/hooks/useTranslator.js';
import {LoaderPanel} from '../../../AppInterface/elements/Loaders/LoaderPanel.js';

export const DsgvoSearchAllResults = ({isLoading, searchword, resultsArray}) => {
    const [t] = useTranslator('DsgvoSearchResults');

    return (
        <>
            <h2>{t('Results_for')}
                <span className={'ui italic'}> "{searchword}"</span>
            </h2>
            <Divider hidden/>
            <Divider/>

            {isLoading ?
                <LoaderPanel size={'large'}/> :
                <>
                    <Divider hidden/>
                    {resultsArray.map((results) =>
                        <Segment size={'large'} key={results.table}>
                            <DsgvoSingleResultPanel results={results}/>
                        </Segment>)}
                </>}
        </>
    );
};



