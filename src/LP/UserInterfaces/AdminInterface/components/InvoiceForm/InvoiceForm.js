import React from 'react';
import {i18n} from '../../Core/i18n.js';
import {successToast} from '../../../AppInterface/elements/Toasts/Toasts.js';
import {useTranslator} from '../../../AppInterface/lib/hooks/useTranslator.js';
import {HookForm} from '../../../AppInterface/elements/HookForm/HookForm.js';
import {yup} from '../../../AppInterface/lib/yup.js';
import {useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup/dist/yup.js';
import {useHookFormErrorsReducer} from '../../../AppInterface/lib/hooks/useHookFormErrorsReducer.js';
import {createInvoice} from "../../api/InvoicesApi";
import {HookFormField} from "../../../AppInterface/elements/HookForm/HookFormField";
import {Input} from "../../../AppInterface/elements/HookForm/Fields/Input/Input";
import {Message} from "semantic-ui-react";
import {CustomerDisplay} from "./CustomerDisplay";
import {Date as DateField} from "../../../AppInterface/elements/HookForm/Fields/Date/Date";

const fields = [
    {
        name: 'serviceFrom',
        label: 'serviceFrom',
        formfield: DateField,
    },
    {
        name: 'serviceTo',
        label: 'serviceTo',
        formfield: DateField,
    },
    {
        name: 'additionalText',
        label: 'additionalText',
        type: 'text'
    },
    {
        name: 'numItems',
        label: 'numItems',
        type: 'number',
        min: 1
    },
    {
        name: 'pricePerItem',
        label: 'pricePerItem',
        type: 'number',
        min: 0,
    }
];
const now = Math.floor((new Date()).getTime() / 1000);
const schema = yup.object().shape({
    serviceFrom: yup.number().default(now).required(),
    serviceTo: yup.number().default(now).required(),
    numItems: yup.number().default(1),
    pricePerItem: yup.number().default(0),
    additionalText: yup.string().default(''),
});

const defaultValues = schema.cast(),
    fieldnames = Object.keys(defaultValues);

export const InvoiceForm = ({accId, onSubmit, ...props}) => {
    const [t] = useTranslator('InvoiceForm'),
        {handleSubmit, formState: {errors: yupErrors}, control, reset} = useForm({
            defaultValues,
            resolver: yupResolver(schema)
        }),
        [errors, setErrors, clearErrors] = useHookFormErrorsReducer({yupErrors, fields: fieldnames});
    const handleOpen = () => {
            reset();
            clearErrors();
        },
        handleSubmitForm = (formData) => createInvoice(accId, formData).then(onSubmit).then(() => successToast(t('Invoice_created')));

    return (
        <HookForm
            submitLabel={t('submit')}
            submitColor={'red'}
            submitIcon={'euro sign'}
            cancelLabel={i18n.t('words.Cancel', {ns: 'common'})}
            title={t('CreateInvoice')}
            errors={t(errors.form)}
            setErrorsFn={setErrors}
            onOpen={handleOpen}
            submitHandler={handleSubmit}
            onSubmit={handleSubmitForm}
            {...props} >
            <Message info icon={'info'} content={t('Info')}/>
            <CustomerDisplay/>
            {fields.map(field => <HookFormField key={field.name} error={t(errors.fields[field.name])}
                                                controller={control}
                                                formfield={Input}
                                                {...field}
                                                label={t(field.label)}/>
            )}
        </HookForm>
    );
};

