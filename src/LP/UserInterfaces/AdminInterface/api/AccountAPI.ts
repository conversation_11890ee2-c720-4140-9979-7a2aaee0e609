import {del, get, post, put} from '../../Core/Request.js';
import {PaymentStatus} from '../../../types/PaymentStatus';

export const deleteAccount = (accId: string, reason: string) => del(`/admin/accounts/${accId}/`, {reason});
export const fetchCustomerData = (accId: string) => get(`/admin/accounts/${accId}/`);
export const getCustomerTransfers = (accId: string) => get(`/admin/accounts/${accId}/transfers`);
export const downgradeToFree = (accId: string) => put(`/admin/accounts/${accId}/downgrade`);
export const exportAccount = (accId: string) => get(`/admin/accounts/${accId}/export`);

export const getCurrentPlan = (accId: string) => get(`/admin/accounts/${accId}/plan`);
export const saveCurrentPlan = (accId: string, data) => put(`/admin/accounts/${accId}/plan`, data);
export const getAccFeatures = (accId: string) => get(`/admin/accounts/${accId}/features`);
export const saveFeatures = (accId: string, features) => put(`/admin/accounts/${accId}/features`, features);
export const getAccSettings = (accId: string) => get(`/admin/accounts/${accId}/settings`);
export const getAccLimits = (accId: string) => get(`/admin/accounts/${accId}/limits`);
export const getAccLogs = (accId: string, params) => get(`/admin/logs/${accId}/`, params);
export const getAccLogsTypes = () => get('/admin/logs/types');
export const getAccLogsPolls = (accId: string) => get(`/admin/logs/${accId}/polls`);
export const getAccLogsUsers = (accId: string) => get(`/admin/logs/${accId}/users`);
export const getAccEmails = (accId: string) => get(`/admin/logs/${accId}/emails`);
export const getAccountHistory = (accId: string) => get(`/admin/accounts/${accId}/acchistory`);
export const getOrderHistory = (accId: string) => get(`/admin/accounts/${accId}/orderhistory`);
export const getOrderByAccId = (accId: string) => get(`/admin/accounts/${accId}/order`);

export const lockAccount = (accId: string) => put(`/admin/accounts/${accId}/lock`);
export const unlockAccount = (accId: string) => put(`/admin/accounts/${accId}/unlock`);

export const allowEmailSending = (accId: string) => put(`/admin/accounts/${accId}/allowEmailSending`);
export const denyEmailSending = (accId: string) => put(`/admin/accounts/${accId}/denyEmailSending`);

export const activate = (accId: string) => put(`/admin/accounts/${accId}/activate`);
export const deactivate = (accId: string) => put(`/admin/accounts/${accId}/deactivate`);

export const denySurveyAccess = (accId: string) => put(`/admin/accounts/${accId}/denySurveyAccess`);
export const allowSurveyAccess = (accId: string) => put(`/admin/accounts/${accId}/allowSurveyAccess`);

export const allowStudentOrder = (accId: string) => put(`/admin/accounts/${accId}/allowStudentOrder`);
export const denyStudentOrder = (accId: string) => put(`/admin/accounts/${accId}/denyStudentOrder`);

export const allowTrialOrder = (accId: string) => put(`/admin/accounts/${accId}/allowTrialOrder`);
export const denyTrialOrder = (accId: string) => put(`/admin/accounts/${accId}/denyTrialOrder`);

export const getAccBillSettings = (accId: string) => get(`/admin/accounts/${accId}/billsettings`);
export const saveAccBillSettings = (accId: string, values) => put(`/admin/accounts/${accId}/billsettings`, values);

export const getListDirectors = (accId: string) => get(`/admin/accounts/${accId}/directors`);
export const transferAccount = (email: string, accId: string) => post(`/admin/accounts/${accId}/forceEmailChange`, {email});

export const getInvoiceStatusSummaryByDate = (from: string, to: string, statuses: Array<PaymentStatus>) => get('/admin/accounting/invoices/summary', {from, to, statuses});