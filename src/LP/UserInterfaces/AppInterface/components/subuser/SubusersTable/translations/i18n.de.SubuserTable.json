{"User": "User", "Last_activity": "Letzte Aktivität", "inactive": "inaktiv", "notifyUser": {"notify": "Benachrichtigen"}, "auth_method": "Auth-<PERSON><PERSON>", "method": {"oauth": "Single Sign On", "password": "Passwort", "email2fa": "<PERSON><PERSON>-Fak<PERSON> via E-Mail", "totp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> via Auth-App (TOTP)"}, "methodLong": {"oauth": "Der Mitarbeiter nutzt Single-Sign-on für den Login.", "password": "Der Mitarbeiter nutzt ein Passwort für den Login.", "email2fa": "Der Mitarbeiter nutzt die Zwei-Faktor-Authentifizierung via E-Mail für den Login.", "totp": "Der Mitarbeiter nutzt die Zwei-Faktor-Authentifizierung via Authenticator-App (TOTP) für den Login."}}