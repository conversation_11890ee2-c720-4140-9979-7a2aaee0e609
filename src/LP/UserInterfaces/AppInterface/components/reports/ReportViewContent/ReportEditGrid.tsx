import {Responsive, WidthProvider} from 'react-grid-layout';
import React, {FC, Suspense, useEffect, useState} from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import './react.Reports.scss';
import {ReportGridItem} from './ReportGridItem';
import {GridItem} from '../../../../../types/ReportGridTable';
import {configureGridItems, getAvailableItemWidth, snapToAllowedWidth} from './reportFunctions';
import {Segment} from 'semantic-ui-react';
import {patchItemPosition} from '../../../lib/api/ReportsAPI';
import {reportGridBreakpoints, reportGridColumns, reportGridItemHeight} from '../../../lib/constants/reports';

interface ReportEditGridProps {
    report: Report;
    gridItems: GridItem[];
    onChange: () => void;
    dataUpdatedAt: number;
    selectedChartItem: string | null;
    selectedTextItem: string | null;
    onChartItemClick: (e: React.MouseEvent, id: string) => void;
    onTextItemClick: (id: string) => void;
}

const ResponsiveGridLayout = WidthProvider(Responsive);
export type GridStatus = 'idle' | 'dragging' | 'resizing';

export const ReportEditGrid: FC<ReportEditGridProps> = ({
                                                            report,
                                                            gridItems = [],
                                                            onChange,
                                                            dataUpdatedAt,
                                                            selectedChartItem,
                                                            selectedTextItem,
                                                            onChartItemClick,
                                                            onTextItemClick,
                                                            ...props
                                                        }) => {
    const [items, setItems] = useState<GridItem[]>(() => configureGridItems(gridItems)),
        [gridStatus, setGridStatus] = useState<GridStatus>('idle'),
        rowErrors = items.filter(item => item.type === 'ph' && [1, 5].includes(item.w)),
        reportId = report.id;


    const layouts = {
        lg: items
    };

    const handleLayoutChange = (layout: GridItem[]): void => {
            setItems(configureGridItems(layout));
        },
        handlePatchPosition = (item: GridItem) => {
            const position = {row: item.y, column: item.x, width: item.w, height: item.h};
            return patchItemPosition(reportId, item.i, item.i, position).finally(onChange);
        },
        handleDragStart = (): void => {
            setGridStatus('dragging');
        },
        handleDrag = (layout: GridItem[], oldItem: GridItem, newItem: GridItem, placeholder: GridItem): void => {
            //The placeholder pof the item must fit into the grid. Item itself need not yet be completely at the new position
            const availableWidth = getAvailableItemWidth(layout, placeholder, false);
            snapToAllowedWidth(oldItem, placeholder, availableWidth, layout, false);
        },
        handleDragStop = (layout: GridItem[], oldItem: GridItem, newItem: GridItem): void => {
            setGridStatus('idle');
            const availableWidth = getAvailableItemWidth(layout, newItem, false);
            const convertedItem = snapToAllowedWidth(oldItem, newItem, availableWidth, layout, false);
            const itemIndex = layout.findIndex(({i}) => i === newItem.i);
            layout[itemIndex] = convertedItem;
            convertedItem.moved && handlePatchPosition(convertedItem);
        },

        handleResizeStart = (): void => {
            setGridStatus('resizing');
        },
        handleResize = (layout: GridItem[], oldItem: GridItem, newItem: GridItem, placeholder: GridItem): void => {
            const availableWidth = getAvailableItemWidth(layout, newItem, false);
            snapToAllowedWidth(oldItem, placeholder, availableWidth, layout, false); //simulate new width
        },
        handleResizeStop = (layout: GridItem[], oldItem: GridItem, newItem: GridItem): void => {
            setGridStatus('idle');
            const availableWidth = getAvailableItemWidth(layout, newItem, false);
            const convertedItem = snapToAllowedWidth(oldItem, newItem, availableWidth, layout, false);
            const itemIndex = layout.findIndex(({i}) => i === newItem.i);
            layout[itemIndex] = convertedItem;
            const wasResized = convertedItem.moved || oldItem.w !== convertedItem.w || oldItem.h !== convertedItem.h;
            wasResized && handlePatchPosition(convertedItem);
        };


    useEffect(() => {
        setItems(configureGridItems(gridItems));
    }, [dataUpdatedAt]);

    //Handles e.g. the case where an item whose width is restricted by an item beneath it to value 2, is placed beside an item with width 3
    if (rowErrors.length > 0) {
        rowErrors.forEach((phItem) => {
            const relevantItems = items.filter(item => item.y === phItem.y);

            relevantItems.forEach(item => {
                const availableWidth = getAvailableItemWidth(items, item, false);
                const convertedItem = snapToAllowedWidth({...item}, {...item}, availableWidth, items, false); //work with copy of items to keep original items unchanged

                if (convertedItem.w !== item.w) {
                    handlePatchPosition(convertedItem);
                }
            });
        });
    }

    return (<>
            <div id={'toolbarContainer'}/>

            <Segment className={'report-wrapper'}>
                <Suspense>
                    <ResponsiveGridLayout
                        className={'editor'}
                        layouts={layouts}
                        onLayoutChange={handleLayoutChange}
                        cols={reportGridColumns}
                        breakpoints={reportGridBreakpoints}
                        compactType={null}
                        rowHeight={reportGridItemHeight}
                        margin={[30, 30]}
                        preventCollision={true}
                        isResizable={true}
                        onResizeStart={handleResizeStart}
                        onResize={handleResize}
                        onResizeStop={handleResizeStop}
                        isDraggable={true}
                        onDragStart={handleDragStart}
                        onDrag={handleDrag}
                        onDragStop={handleDragStop}
                        allowOverlap={true}
                        useCSSTransforms={true}
                        {...props}
                    >


                        {items.map((item) =>
                            <ReportGridItem key={item.i}
                                            item={item}
                                            itemData={gridItems.find(elem => elem.i === item.i) || {type: null, reportId}}
                                            onChange={onChange}
                                            onChartItemClick={onChartItemClick}
                                            onTextItemClick={onTextItemClick}
                                            className={`report-grid-item ${items.length === 1 ? 'empty-report' : ''}`}
                                            report={report}
                                            selectedChartItem={selectedChartItem}
                                            selectedTextItem={selectedTextItem}
                                            gridStatus={gridStatus}
                            />)}


                    </ResponsiveGridLayout>
                </Suspense>

            </Segment>

        </>

    );
};