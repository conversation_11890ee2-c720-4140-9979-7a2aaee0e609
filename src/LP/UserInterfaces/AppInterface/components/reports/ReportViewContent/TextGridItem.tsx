import React from 'react';
import './react.Reports.scss';
import {patchTextItem} from '../../../lib/api/ReportsAPI';
import {GridItemProps} from './ReportGridItem';
import {ReportTextEditor} from '../../FroalaTextEditors/ReportTextEditor/ReportTextEditor';
import {useTranslator} from '../../../lib/hooks/useTranslator';
import {IconButton} from '../../../elements/Buttons/IconButton';
import {ReportItemDeleteConfirm} from '../ReportItemDeleteConfirm/ReportItemDeleteConfirm';


export const TextGridItem = React.forwardRef<HTMLDivElement, GridItemProps>(({item, selectedTextItem, itemData, onChange, gridStatus, style, className, children, onTextItemClick, onMouseDown, onMouseUp, onTouchEnd}, ref) => {
    const [t] = useTranslator('ReportEditor');
    const handleTextItemClick = () => {
            onTextItemClick?.(item.i);
        },
        handlePatchReport = (_e, {text}) => patchTextItem(itemData?.reportId, item?.i, {text});

    return (
        <div style={{...style}} id={item?.i} className={`${className} ${gridStatus} ${item.i === selectedTextItem ? 'selected' : ''} text-grid-item`} ref={ref} onMouseDown={onMouseDown} onMouseUp={onMouseUp} onTouchEnd={onTouchEnd}>
            <div className={'report-item-container'} onClick={handleTextItemClick}>
                <ReportTextEditor reportId={itemData?.reportId}
                                  content={itemData?.text}
                                  placeholderText={t('textItem.placeholder')}
                    // onFocus={handleFocus}
                                  onBlur={handlePatchReport}
                />
                {children}
            </div>

            <span className={'grid-item-buttons'}>

                <ReportItemDeleteConfirm key={'delete'}
                                         onDelete={onChange}
                                         reportId={itemData?.reportId}
                                         itemId={item?.i}
                                         trigger={<IconButton title={t('iconButton.deleteItem')}
                                                              icon={'trash alternate outline'}
                                                              size={'mini'}
                                                              noColor/>}/>
            </span>
        </div>
    );
});

TextGridItem.displayName = 'TextGridItem';