import React, {FC} from 'react';
import './react.Reports.scss';
import {ReportEditGrid} from './ReportEditGrid';
import {patchReport} from '../../../lib/api/ReportsAPI';
import {InlineInput} from '../../../elements/Inputs/InlineInput';
import {MenuPanel} from './MenuPanel';
import {useTranslator} from '../../../lib/hooks/useTranslator';
import {useFormErrorsReducer} from '../../../lib/hooks/useFormErrorsReducer';
import {Report} from '../../../../../typings/Report';
import {ReportChartItem, ReportTextItem} from '../../../../../typings/ReportItem';

interface ReportViewContentProps {
    report: Report;
    items: Array<ReportChartItem | ReportTextItem>;
    dataUpdatedAt: number;
    refetchReportItems: () => void;
    selectedChartItem: string | null;
    selectedTextItem: string | null;
    onOpenChartEditor: (id: string) => void;
    onTextItemClick: (id: string) => void;
}

export const ReportViewContent: FC<ReportViewContentProps> = ({report, items, selectedChartItem, selectedTextItem, dataUpdatedAt, refetchReportItems, onOpenChartEditor, onTextItemClick}) => {
    const [t] = useTranslator('ReportEditor'),
        [errors, setErrors, clearErrors] = useFormErrorsReducer(['name', 'notice']);

    const handlePatchReport = (e, {name, value}) => {
        clearErrors(name);

        if (name === 'name' && value.length < 3) {
            setErrors({name: 'too_short'});
            return;
        }
        patchReport(report.id, {[name]: value}).catch(({errors}) => setErrors(errors));
    };

    return (
        <>
            <MenuPanel report={report}/>
            <InlineInput className={'text massive bold'} name={'name'} value={report.name} placeholder={t('placeholder.reportName')} onChange={handlePatchReport} error={errors.fields.name} minLength={3}/>
            <InlineInput className={'text grey bold'} name={'notice'} value={report.notice} placeholder={t('placeholder.reportNotice')} onChange={handlePatchReport} error={errors.fields.notice}/>
            <ReportEditGrid report={report}
                            onOpenChartEditor={onOpenChartEditor}
                            onTextItemClick={onTextItemClick}
                            dataUpdatedAt={dataUpdatedAt}
                            gridItems={items}
                            selectedChartItem={selectedChartItem}
                            selectedTextItem={selectedTextItem}
                            onChange={refetchReportItems}/>

            {/*<JsonChart/>*/}
        </>
    );
};

