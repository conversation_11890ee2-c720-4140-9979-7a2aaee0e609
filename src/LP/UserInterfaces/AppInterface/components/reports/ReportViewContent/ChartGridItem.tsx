import React, {useRef} from 'react';
import './react.Reports.scss';
import {GridItemProps} from './ReportGridItem';
import {IconButton} from '../../../elements/Buttons/IconButton';
import {ReportItemDeleteConfirm} from '../ReportItemDeleteConfirm/ReportItemDeleteConfirm';
import {useTranslator} from '../../../lib/hooks/useTranslator';
import {ReportChartItem} from '../../../../Common/Components/reports/ReportChartItem/ReportChartItem';

// More comprehensive list of clickable elements
const clickableElements = [
    'button',
    'i',
    'a',
    'rect[class*="highcharts-button-box"]',
    'path[class*="highcharts-button-symbol"]',
    'li[class*="highcharts-menu-item"]',
    'g[class*="highcharts-series"]',
    'path[class*="highcharts-point"]',
    'circle[class*="highcharts-point"]',
    'rect[class*="highcharts-point"]',
    'g[class*="highcharts-tooltip"]',
    'div[class*="highcharts-tooltip"]',
    'g[class*="highcharts-legend-item"]',
    'text[class*="highcharts-title"]',
    'g[class*="highcharts-axis"]',
    'g[class*="highcharts-plot-lines"]',
    'g[class*="highcharts-plot-bands"]'
];

// Define minimum movement that constitutes a drag (in pixels)
const DRAG_THRESHOLD = 5;
export const ChartGridItem = React.forwardRef<HTMLDivElement, GridItemProps>(({report, selectedChartItem, item, itemData, onChange, gridStatus, style, className, children, onMouseDown, onMouseUp, onTouchEnd, onChartItemClick}, ref) => {
    const [t] = useTranslator('ReportEditor');
    const mouseDownPos = useRef<{ x: number; y: number } | null>(null);
    const mouseDownTarget = useRef<EventTarget | null>(null);
    const isChartElement = useRef<boolean>(false);

    const isClickableElement = (target: EventTarget | null): boolean => {
        if (!target || !(target instanceof Element)) return false;

        if (clickableElements.some(selector => target.matches(selector))) {
            return true;
        }

        let parent = target.parentElement;
        while (parent) {
            if (clickableElements.some(selector => parent?.matches(selector))) {
                return true;
            }
            parent = parent.parentElement;
        }

        return false;
    };

    const handleMouseDown = (e: React.MouseEvent) => {
        mouseDownPos.current = {x: e.clientX, y: e.clientY};
        mouseDownTarget.current = e.target;
        isChartElement.current = isClickableElement(e.target);

        if (!isChartElement.current) {
            onMouseDown?.(e);
        }
    };

    const handleMouseUp = (e: React.MouseEvent) => {
        // If no mouseDownPos, something went wrong
        if (!mouseDownPos.current) return;

        // Calculate movement distance
        const deltaX = Math.abs(e.clientX - mouseDownPos.current.x);
        const deltaY = Math.abs(e.clientY - mouseDownPos.current.y);
        const totalMovement = Math.sqrt((deltaX * deltaX) + (deltaY * deltaY));

        // Reset the position
        mouseDownPos.current = null;

        // If we clicked on a chart element, let the event propagate
        if (isClickableElement(e.target)) {
            return;
        }

        // If movement is less than threshold, consider it a click
        if (totalMovement < DRAG_THRESHOLD && !isChartElement.current) {
            onChartItemClick?.(e, item.i);
            return;
        }

        onMouseUp?.(e);
    };

    // Handle touch events similarly
    const handleTouchStart = (e: React.TouchEvent) => {
        const touch = e.touches[0];
        mouseDownPos.current = {x: touch.clientX, y: touch.clientY};
        mouseDownTarget.current = e.target;
        isChartElement.current = isClickableElement(e.target);

        if (!isChartElement.current) {
            onMouseDown?.(e);
        }
    };
    const handleTouchEnd = (e: React.TouchEvent) => {
        if (!mouseDownPos.current) return;

        const touch = e.changedTouches[0];
        const deltaX = Math.abs(touch.clientX - mouseDownPos.current.x);
        const deltaY = Math.abs(touch.clientY - mouseDownPos.current.y);
        const totalMovement = Math.sqrt((deltaX * deltaX) + (deltaY * deltaY));

        mouseDownPos.current = null;

        if (isClickableElement(e.target)) {
            return;
        }

        if (totalMovement < DRAG_THRESHOLD && !isChartElement.current) {
            onChartItemClick?.(e, item.i);
            return;
        }

        onTouchEnd?.(e);
    };

    return (

        <div
            style={{...style}}
            id={item.i}
            className={`${className} ${gridStatus} ${item.i === selectedChartItem ? 'selected' : ''} chart-grid-item`}
            ref={ref}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
        >
            <div className={'report-item-container'}>
                <ReportChartItem
                    reportId={report.id}
                    item={itemData}
                    allowChartUpdate={gridStatus !== 'dragging'}
                />
                {children}
            </div>

            <span className={'grid-item-buttons'}>
                <ReportItemDeleteConfirm
                    key={'delete'}
                    onDelete={onChange}
                    reportId={itemData?.reportId}
                    itemId={item.i}
                    trigger={
                        <IconButton
                            title={t('iconButton.deleteItem')}
                            icon={'trash alternate outline'}
                            size={'mini'}
                            noColor
                        />
                    }
                />
            </span>
        </div>
    );
});

ChartGridItem.displayName = 'ChartGridItem';
