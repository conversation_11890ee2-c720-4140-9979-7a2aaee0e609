import React from 'react';
import {useForm} from 'react-hook-form';
import {useTranslator} from '../../lib/hooks/useTranslator.js';
import {Divider} from 'semantic-ui-react';
import {saveOAuthContact} from '../../lib/api/OAuthAPI.js';
import {HookFormField} from '../../elements/HookForm/HookFormField.js';
import {yup} from '../../lib/yup.js';
import {yupResolver} from '@hookform/resolvers/yup';
import {HookForm} from '../../elements/HookForm/HookForm.js';
import {Input} from '../../elements/HookForm/Fields/Input/Input.js';
import {Checkbox} from '../../elements/HookForm/Fields/Checkbox/Checkbox.js';
import {useHookFormErrorsReducer} from '../../lib/hooks/useHookFormErrorsReducer.js';

const fields = [
    {
        label: 'email.label',
        name: 'email',
        placeholder: 'email.placeholder',
        required: true,
        autofocus: true,
        type: 'email'
    },
    {label: 'name.label', name: 'name', placeholder: 'name.placeholder', required: false, type: 'text'},
    {label: 'phone.label', name: 'phone', placeholder: 'phone.placeholder', required: false, type: 'text'}
];

const schema = yup.object().shape({
        email: yup.string().required(true).email('not_email').default(''),
        name: yup.string().ensure().when('phone', {
            is: '',
            then: (schema) => schema.required('nameOrPhoneRequired')
        }).default(''),
        phone: yup.string().when({
            is: (val) => !!val,
            then: (schema) => schema.isPhoneNumber()
        }).ensure().when('name', {
            is: '',
            then: (schema) => schema.required('nameOrPhoneRequired')
        }).default(''),
        agree2publish: yup.boolean().oneOf([true], true).required(true).default(false)
    }, [['name', 'phone']]),
    defaultValues = schema.cast(),
    fieldnames = Object.keys(defaultValues);

export const OAuthContactForm = ({onConfirm, contact, ...props}) => {
    const {handleSubmit, formState: {errors: yupErrors}, setValue, control, reset} = useForm({
            defaultValues,
            resolver: yupResolver(schema)
        }),
        [t] = useTranslator('OAuthContactForm'),
        [errors, setErrors, clearErrors] = useHookFormErrorsReducer({yupErrors, fields: fieldnames});

    const handleOpen = () => {
            clearErrors();
            reset();
            contact && fieldnames.map(k => setValue(k, contact[k]));
        },
        handleSubmitContact = contactData => saveOAuthContact(contactData).then(onConfirm);

    return <HookForm
        errors={t(errors.form)}
        setErrorsFn={setErrors}
        submitLabel={t('words.Save', {ns: 'common'})}
        cancelLabel={t('words.Cancel', {ns: 'common'})}
        title={t('title')}
        onOpen={handleOpen}
        submitHandler={handleSubmit}
        onSubmit={handleSubmitContact}
        noValidate
        {...props}
    >
        {t('desc')}
        <Divider hidden/>
        {fields.map(field =>
            <HookFormField
                {...field}
                controller={control}
                formfield={Input}
                key={field.name}
                error={t(errors.fields[field.name])}
                placeholder={t(field.placeholder)}
                label={t(field.label)}
            />)}
        <Divider hidden/>

        <HookFormField
            controller={control}
            formfield={Checkbox}
            error={t(errors.fields.agree2publish, {keyPrefix: 'agree2Puplish'})}
            value={1}
            name={'agree2publish'}
            label={t('agree2publish.label')}

        />
    </HookForm>;
};