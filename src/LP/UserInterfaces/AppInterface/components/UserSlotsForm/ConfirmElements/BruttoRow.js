import React from 'react';
import {TableCell, TableRow} from "semantic-ui-react";
import {useTranslator} from "../../../lib/hooks/useTranslator";
import {Currency} from "../../../elements/Formatters/Currency/Currency";
import {useDeviceStyleDetection} from "../../../lib/hooks/useDeviceStyleDetection";

export const BruttoRow = ({numberOfUsers, userPrice}) => {
    const
        [t] = useTranslator('UserSlotsForm'),
        {screen} = useDeviceStyleDetection();

    return (
            <TableRow>
                {screen == 'mobile' ? (
                        <TableCell collapsing>{t('employeeSlots.labelMobile', {count: numberOfUsers})}</TableCell>
                ) : (
                    <>
                        <TableCell>{t('employeeSlots.label')}</TableCell>
                        <TableCell collapsing textAlign={"right"}>{numberOfUsers}</TableCell>
                    </>
                )}

                <TableCell collapsing textAlign={"right"}><Currency quantity={userPrice.base}/></TableCell>
                <TableCell collapsing textAlign={"right"}><Currency quantity={userPrice.base * numberOfUsers}/></TableCell>
            </TableRow>
    );
};
