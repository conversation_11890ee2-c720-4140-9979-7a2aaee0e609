import {ResponsesDevice} from "../../lib/classes/ResponsesDevice";

const iconObj = {
    windows: 'windows',
    mac: 'apple',
    linux: 'linux',
    android: 'android',
    ios: 'apple',
};


export const getStatisticsDevices = (deviceStatistics, t) => {
    const devices = new ResponsesDevice(deviceStatistics);

    return [
        {
            name: 'desktop',
            icon: 'computer',
            headerTxt: t('desktop'),
            percent: devices.getDevicesPercentForDeviceType('PC'),
            oss: ['windows', 'mac', 'linux'].map(os => ({
                name: os,
                percent: devices.getOSPercentForDeviceType('PC', os.toUpperCase()),
                browsers: devices.getBrowsersForDeviceTypeAndOSType('PC', os.toUpperCase()),
                icon: iconObj[os]
            }))
        },
        {
            name: 'tablet',
            icon: 'tablet alternate',
            headerTxt: t('tablet'),
            percent: devices.getDevicesPercentForDeviceType('TABLET'),
            oss: ['android', 'ios', 'other'].map(os => ({
                name: os,
                percent: devices.getOSPercentForDeviceType('TABLET', os.toUpperCase()),
                browsers: devices.getBrowsersForDeviceTypeAndOSType('TABLET', os.toUpperCase()),
                icon: iconObj[os]
            }))

        },
        {
            name: 'mobile',
            icon: 'mobile alternate',
            headerTxt: t('mobile'),
            percent: devices.getDevicesPercentForDeviceType('MOBILE'),
            oss: ['android', 'ios', 'other'].map(os => ({
                name: os,
                percent: devices.getOSPercentForDeviceType('MOBILE', os.toUpperCase()),
                browsers: devices.getBrowsersForDeviceTypeAndOSType('MOBILE', os.toUpperCase()),
                icon: iconObj[os]
            }))
        }
    ];
};