import FroalaEditor from 'froala-editor';
import {toolbarButtons} from './toolbars.js';
import {addUploadInfo} from '../froalaTextEditors';
import {deleteImage, deleteVideo} from '../../../lib/api/ReportsAPI';
import {errorToast} from '../../../elements/Toasts/Toasts';

export const config = (reportId, lang, placeholderText, onBlur, configOptions = {}) => ({
    initOnClick: true,
    toolbarButtons: toolbarButtons,
    language: lang,
    toolbarInline: true,
    toolbarVisibleWithoutSelection: true,
    placeholderText: placeholderText,
    enter: FroalaEditor.ENTER_BR,
    useClasses: true,
    // fontFamily: getFroalaFonts(fonts),
    height: '100%',
    imageUploadURL: `/reports/${reportId}/images`,
    imageUploadMethod: 'PUT',
    imageUpload: true,
    imageManagerLoadURL: `/reports/${reportId}/images/`,
    imageManagerLoadMethod: 'GET',
    imageManagerDeleteURL: `/reports/${reportId}/images/`,
    imageManagerDeleteMethod: 'DELETE',
    videoUploadURL: `/reports/${reportId}/videos`,
    videoUploadMethod: 'PUT',
    events: {
        'blur': function (e) {
            console.log('sdsfsd');
            // Add debounce to prevent multiple triggers
            if (this._lastBlur && Date.now() - this._lastBlur < 100) {
                return;
            }
            this._lastBlur = Date.now();

            onBlur(e, {text: this.html.get()});
        },
        'video.inserted': function ($video) {
            $video.find('video').attr('playsinline', true);
        },
        'video.removed': ($video) => {
            const src = decodeURI($video.attr('src'));
            //delete only for uploaded videos, not for iframes
            if (src.includes(`/reports/${reportId}/videos/`)) {
                deleteVideo(reportId, src).catch(() => {
                    errorToast('error deleting video');
                });
            }
        },
        'image.removed': ($img) => {
            const src = decodeURI($img.attr('src'));
            deleteImage(reportId, src).catch(() => {
                errorToast('error deleting image');
            });
        },

        'commands.after': function (cmd) {
            if (cmd == 'insertVideo' || cmd == 'insertImage') {
                const pluginName = cmd == 'insertVideo' ? 'video' : 'image';
                addUploadInfo(pluginName);
            }
        }
    },
    ...configOptions
});