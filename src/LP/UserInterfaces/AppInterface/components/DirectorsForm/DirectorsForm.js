import React, {useReducer, useState} from 'react';
import {mergeReducer} from '../../lib/reducer/mergeReducer.js';
import {Form} from '../../elements/Form';
import {useTranslator} from '../../lib/hooks/useTranslator.js';
import {defaults, DirectorsFormFields, fieldnames} from './DirectorsFormFields.js';
import {Message} from 'semantic-ui-react';
import HTMLReactParser from 'html-react-parser';
import {getDirectors, saveDirector} from '../../lib/api/DirectorsAPI.js';
import {call} from '../../lib/helpers/callFunction.js';
import {useUser} from '../../lib/contexts/AuthContext.js';
import {useFormErrorsReducer} from '../../lib/hooks/useFormErrorsReducer.js';

export const DirectorsForm = ({directorId, onSubmit, children, ...props}) => {
    const user = useUser(),
        [values, setValues] = useReducer(mergeReducer, defaults),
        [errors, setErrors, clearErrors] = useFormErrorsReducer(fieldnames),
        [isLoading, setIsLoading] = useState(false),
        [t] = useTranslator('DirectorsForm');

    const handleOpen = () => {
            setValues(defaults);
            clearErrors();
            if (directorId) {
                setIsLoading(true);
                getDirectors(directorId)
                    .then(data => {
                        setValues(data);
                        setIsLoading(false);
                    });
            }
        },
        handleChange = (event, {name, value}) => {
            setValues({...values, [name]: value});
            clearErrors(name);
        },
        validate = () => {
            if (values.name == '') {
                setErrors({name: true});
                return false;
            } else if (values.email == '' && values.phone == '') {
                setErrors({form: ['enter_contact']});
                return false;
            }
            return true;
        },
        handleSubmit = () => new Promise((resolve, reject) => {
            if (validate()) {
                saveDirector(values, directorId)
                    .then((...data) => {
                        call(onSubmit, ...data);
                        resolve();
                    })
                    .catch(({errors}) => {
                        setErrors(errors);
                        reject();
                    });
            } else {
                reject();
            }
        });

    return (
        <Form
            submitLabel={t('words.Save', {ns: 'common'})}
            cancelLabel={t('words.Cancel', {ns: 'common'})}
            title={t('director')}
            isLoading={isLoading}
            onOpen={handleOpen}
            onSubmit={handleSubmit}
            errors={t(errors.form)}
            autoComplete={'off'}
            {...props}>
            {children}
            <DirectorsFormFields values={values} onChange={handleChange} errors={errors.fields}/>
            <Message info>{HTMLReactParser(t('confirm_save_director_info', {email: user.email}))}</Message>
        </Form>
    );
};
