import React from 'react';
import {TextArea} from 'semantic-ui-react';
import {FormField} from '../../elements/Form';
import {useTranslator} from '../../lib/hooks/useTranslator.js';

export const defaults = {reason: ''};
export const fieldnames = Object.keys(defaults);

export const OrderTerminateFormFields = ({values, onChange, errors}) => {
    const {reason} = values;

    const [t] = useTranslator('OrderTerminateForm');

    return (
        <FormField control={TextArea}
                   name={'reason'}
                   type={'text'}
                   label={t('reason.label')}
                   autoComplete={'off'}
                   placeholder={t('reason.placeholder')}
                   value={reason}
                   onChange={onChange}
                   autoFocus
                   error={t(errors.reason, {ns: 'Errors'})}/>
    );
};
