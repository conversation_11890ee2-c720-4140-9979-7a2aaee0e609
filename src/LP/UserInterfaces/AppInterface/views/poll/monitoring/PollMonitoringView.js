import React from 'react';
import {useTranslator} from '../../../lib/hooks/useTranslator.js';
import {PanelContainer} from '../../../elements/containers/PanelContainer.js';
import {DocumentTitle} from '../../../elements/DocumentTitle/DocumentTitle.js';
import {CenteredContentLayout} from '../../../layouts/contentLayouts/CenteredContentLayout.tsx';
import {ViewHeadline} from '../../../elements/headlines/ViewHeadline.js';
import {usePoll} from "../../../../Common/React/lib/contexts/PollContext";
import {PollMonitoringJobPanel} from "../../../components/PollMonitoringJobPanel/PollMonitoringJobPanel";

export default function PollMonitoringView() {
    const [t] = useTranslator('PollMonitoringView'),
        {poll} = usePoll();


    return (
        <CenteredContentLayout>
            <DocumentTitle title={t('Monitoring') + ": " + poll.name}/>

            <ViewHeadline content={t('Monitoring')}/>

            <PanelContainer>
                <PollMonitoringJobPanel/>
            </PanelContainer>
        </CenteredContentLayout>
    );
}
