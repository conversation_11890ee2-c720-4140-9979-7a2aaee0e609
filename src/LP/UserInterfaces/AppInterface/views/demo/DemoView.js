import React from 'react';
import {DemoForm} from '../../components/DemoForm/DemoForm.js';
import {IconButton} from '../../elements/Buttons/IconButton.js';
import {CenteredContentLayout} from '../../layouts/contentLayouts/CenteredContentLayout.tsx';

export default function DemoView() {
    return (
        <CenteredContentLayout documentTitle={'Hookform - Demo'}>
            <DemoForm asModal trigger={
                <IconButton basic icon={'plus'} label={'Open Form'}/>}/>
        </CenteredContentLayout>
    );
}
