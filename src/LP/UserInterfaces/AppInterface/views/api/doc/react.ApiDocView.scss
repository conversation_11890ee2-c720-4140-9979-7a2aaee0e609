@use '../../../../Common/Styles/_colors.scss';

.ui img#apiLogo {
  width: 200px;
  margin-right: 1rem;
}

.view-apidoc {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;

  .pushable {
    border: 1px solid lightgrey;
    overflow-y: scroll;
  }

  .sidebarhandler {
    position: absolute;
    right: -20px;
    top: -27.5em;
    width: 50px;
    height: 53em;
  }

  .ui.left.sidebar.sidebarInactive {
    transform: translate3d(-210px, 0, 0);
    visibility: visible;
    overflow: hidden !important;
    @media(max-width: 767px) {
      transform: translate3d(-100%, 0, 0);
    }
  }

  .ui.left.sidebar.sidebarActive {
    overflow-x: hidden;
  }

  @media print {
    .pushable, .left.sidebar ~ .pusher {
      transform: none !important;
      border: 0 !important;
      padding: 0 !important;
    }
    .left.sidebar, .api-header {
      display: none !important;
    }
  }
}