import {QueryClient, useMutation, UseMutationResult} from '@tanstack/react-query';
import {merge} from 'lodash';
import {patchChartItem} from '../api/ReportsAPI';
import {TChartMutationData} from '../../../../types/TChartItem';

interface UseChartItemMutationProps {
    report: {
        id: string;
        lang: string;
    };
    itemId: string;
    queryClient: QueryClient;
}

interface ChartItemMutationData {
    options?: {
        stacked?: boolean;
        [key: string]: any;
    };

    [key: string]: any;
}

export const useChartItemMutation = ({report, itemId, queryClient}: UseChartItemMutationProps): UseMutationResult<any, Error, TChartMutationData> => {
    return useMutation({
        mutationKey: ['patchChartItem', report.id, itemId],
        mutationFn: (configValueObj: ChartItemMutationData) => new Promise((resolve, reject) => {
            return patchChartItem(report.id, itemId, {...configValueObj, lang: report.lang}).then(resolve).catch(reject);
        }),
        onMutate: async (configValueObj) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({queryKey: ['getChartItem', report.id, itemId], exact: true});
            // Snapshot the previous value
            const previousData = queryClient.getQueryData(['getChartItem', report.id, itemId]);
            // Optimistically update to the new value
            const newData = merge({}, previousData, configValueObj);
            queryClient.setQueryData(['getChartItem', report.id, itemId], newData);
            // Return a context object with the snapshotted value
            return {previousData};
        },
        onError: (_err, _newSettings, context) => {
            // Rollback to the previous value if mutation fails
            queryClient.setQueryData(['getChartItem', report.id, itemId], context?.previousData);
        },
        onSettled: () => {
            // Refetch after error or success to ensure data is correct
            queryClient.refetchQueries({queryKey: ['getChartItem', report.id, itemId], exact: true});
        }
    });
};
