import React from 'react';
import {screen} from '@testing-library/react';
import {AuthContext} from '../../lib/contexts/AuthContext';
import {PollContext} from '../../../Common/React/lib/contexts/PollContext';
import {getMockedAuthContext} from '../../../../../../tests/jest/lib/mockedAuthContext';
import {renderWithRouter} from '../../../../../../tests/jest/lib/renderWithRouter';
import {PollSidebarMenu} from './PollSidebarMenu';
import {getMockedPollContext} from '../../../../../../tests/jest/lib/getMockedPollContext';


describe('PollSidebarMenu', () => {

    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('should not show reports', async () => {
        renderWithRouter(
            <AuthContext.Provider value={getMockedAuthContext('ENTERPRISE_A', 1)}>
                <PollContext.Provider value={getMockedPollContext()}>
                    <PollSidebarMenu isMobileMenuVisible={false}/>
                </PollContext.Provider>
            </AuthContext.Provider>,
            {route: '#/poll/18/evaluate/general', path: 'poll/:pollId/:tab/:mode'}
        );

        expect(await screen.findByText('Dashboard')).toBeInTheDocument();


        // Verify other common menu items are visible
        expect(await screen.findByText('Results')).toBeInTheDocument();
        expect(await screen.findByText('Compare')).toBeInTheDocument();
        expect(await screen.findByText('Samples')).toBeInTheDocument();
        expect(await screen.findByText('Monitoring')).toBeInTheDocument();
        expect(await screen.findByText('Codings')).toBeInTheDocument();

        // Verify Reports is NOT visible
        expect(screen.queryByText('Reports')).not.toBeInTheDocument();
    });
});