import React from 'react';
import {Divider} from 'semantic-ui-react';
import {PollSidebarMenuItem} from './PollSidebarMenuItem';
import {usePoll} from '../../../../Common/React/lib/contexts/PollContext';
import {useIdCard} from '../../../lib/contexts/AuthContext';
import {MenuItem} from './PollSidebarMenu.types';
import {ReactJSX} from '@emotion/react/dist/declarations/src/jsx-namespace';

interface PollPublishItems {
    publishSettings: MenuItem[];
    publishMethods: MenuItem[];
    publishInvitation: MenuItem[];
};
export const PollPublishMenu = () => {
    const {poll} = usePoll(),
        idCard = useIdCard();

    const pollPublishMenuItems: PollPublishItems = {
        publishSettings: [
            {
                name: 'Publish_Overview',
                path: `/poll/${poll.id}/publish/general`,
                icon: 'bullhorn',
                toBeDisplayed: true,
            },
            {
                name: 'Settings',
                path: `/poll/${poll.id}/publish/settings`,
                icon: 'setting',
                toBeDisplayed: true,
                disabled: !poll.permissions.publish
            },
        ],
        publishMethods: [
            {
                name: 'Links_&_QR_codes',
                path: `/poll/${poll.id}/publish/polllink`,
                icon: 'qrcode',
                toBeDisplayed: true
            },
            {
                name: 'Embed',
                path: `/poll/${poll.id}/publish/embed`,
                icon: 'globe',
                toBeDisplayed: true,
                disabled: !poll.permissions.publish
            },
            {
                name: 'Accesskeys',
                path: `/poll/${poll.id}/publish/accesskeys`,
                icon: 'key',
                toBeDisplayed: true,
                disabled: !poll.permissions.publish
            },
        ],
        publishInvitation: [
            {
                name: 'Addressbook',
                path: `/poll/${poll.id}/publish/addressbook`,
                icon: 'address book outline',
                toBeDisplayed: true,
                locked: !idCard.hasFeature('canUseAddressBooks'),
                disabled: idCard.hasFeature('canUseAddressBooks') && !poll.permissions.accessAddressBook
            },
            {
                name: 'Emails',
                path: `/poll/${poll.id}/publish/emails?sort=modified&order=desc`,
                icon: 'envelope open outline',
                toBeDisplayed: true,
                locked: !idCard.hasFeature('canUseAddressBooks'), //The feature 'canUseAddressBooks' applies to all three views - Addressbook, Emails and Mailing
                disabled: idCard.hasFeature('canUseAddressBooks') && !poll.permissions.publish
            },
            {
                name: 'Mailing',
                path: `/poll/${poll.id}/publish/mailing`,
                icon: 'send',
                toBeDisplayed: true,
                locked: !idCard.hasFeature('canUseAddressBooks'), //The feature 'canUseAddressBooks' applies to all three views - Addressbook, Emails and Mailing
                disabled: idCard.hasFeature('canUseAddressBooks') && !poll.permissions.publish
            },
        ]
    };

    return (
        <>
            {pollPublishMenuItems.publishSettings.map((item): ReactJSX.Element | false => item.toBeDisplayed &&
                <PollSidebarMenuItem key={item.name} item={item}/>)}
            <Divider/>
            {pollPublishMenuItems.publishMethods.map((item): ReactJSX.Element | false => item.toBeDisplayed &&
                <PollSidebarMenuItem key={item.name} item={item}/>)}
            <Divider/>
            {pollPublishMenuItems.publishInvitation.map((item): ReactJSX.Element | false => item.toBeDisplayed &&
                <PollSidebarMenuItem key={item.name} item={item}/>)}
        </>
    );
};