<?php

namespace <PERSON><PERSON>\LP\Admin\Invoices;

use Lamano\LP\App\Invoices\Models\InvoiceItem;
use Lamano\LP\App\Policies\Policy;

class InvoiceItemPolicy extends Policy
{
    public function canList(): bool
    {
        return $this->identityCard->isStaffUser();
    }

    public function canCreate(): bool
    {
        return $this->identityCard->isAccountingUser() || $this->identityCard->isAdmin();
    }

    public function canRead(InvoiceItem $invoiceItem): bool
    {
        return $this->identityCard->isStaffUser();
    }

    public function canUpdate(InvoiceItem $invoiceItem): bool
    {
        return false;
    }

    public function canDelete(InvoiceItem $invoiceItem): bool
    {
        return false;
    }
}
