<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\LP\Core\File;

use <PERSON>no\Core\Enums\MimeTypes;
use <PERSON>no\Core\File\Directory;
use <PERSON>no\Core\File\File;
use Stringable;

class UploadedFile implements Stringable
{
    protected $resource;
    protected ?File $file = null;
    protected MimeTypes $type = MimeTypes::TEXT;
    protected string $origName = '';
    protected int $error = UPLOAD_ERR_OK;

    public function __construct(string $content, MimeTypes $mimeType = null, string $fileName = '')
    {
        $this->resource = tmpfile();
        if (!$this->resource) {
            $this->error = UPLOAD_ERR_NO_TMP_DIR;
            return;
        }

        $path = stream_get_meta_data($this->resource)['uri'];
        $this->file = new File($path);
        $this->origName = $fileName;

        if (!$this->file->write($content)) {
            $this->error = UPLOAD_ERR_CANT_WRITE;
        }

        if ($mimeType === null) {
            $detectedType = mime_content_type($this->file->fullPath());
            $mimeType = MimeTypes::tryFrom(
                $detectedType
            ) ?: MimeTypes::OCTET_STREAM; // octet-stream is a good default fallback for (presumably) binary data
        }

        $this->type = $mimeType;

        register_shutdown_function([$this, 'delete']);
    }

    /**
     * Returns the error code of the file upload
     *
     * @return int
     *
     * @see UPLOAD_ERR_OK
     * @see UPLOAD_ERR_INI_SIZE
     * @see UPLOAD_ERR_FORM_SIZE
     * @see UPLOAD_ERR_PARTIAL
     * @see UPLOAD_ERR_NO_FILE
     * @see UPLOAD_ERR_NO_TMP_DIR
     * @see UPLOAD_ERR_CANT_WRITE
     * @see UPLOAD_ERR_EXTENSION
     */
    public function getError(): int
    {
        return $this->error;
    }

    /**
     * Deletes the uploaded file
     *
     * Also happens automatically at the end of the request
     *
     * @return bool
     */
    public function delete(): bool
    {
        return $this->file->delete();
    }

    /**
     * Copies the uploaded file to the given path
     *
     * @param string $path
     *
     * @return ?File Returns a {@see File} object on success, or null on failure
     */
    public function copyTo(string $path): ?File
    {
        if ($this->error !== UPLOAD_ERR_OK) {
            return null;
        }

        $directory = new Directory(dirname($path));
        $directory->create();

        $targetFile = new File($path);
        $success = $targetFile->write($this->file->read());
        return $success ? $targetFile : null;
    }

    /**
     * Returns a {@see File} object for the uploaded file
     *
     * @return ?File returns null if the file was not uploaded successfully, see {@see UploadedFile::getError()}
     */
    public function getTempFile(): ?File
    {
        return $this->file;
    }

    /**
     * Returns the MimeType for the uploaded file
     */
    public function getType(): MimeTypes
    {
        return $this->type;
    }

    /**
     * Returns the name with which the file got uploaded
     *
     * @return string
     */
    public function getOriginalName(): string
    {
        return $this->origName;
    }

    public function __toString(): string
    {
        return $this->file->fullPath();
    }
}
