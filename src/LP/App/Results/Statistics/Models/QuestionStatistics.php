<?php

declare(strict_types=1);

namespace Lamano\LP\App\Results\Statistics\Models;

class QuestionStatistics extends Statistics
{
    public function __construct(
        protected int $questionId,
        protected int $pageId,
        protected int $postion,
        protected array $data
    ) {
        parent::__construct($data);
    }

    public function jsonSerialize(): array
    {
        return array_merge(
            [
                'id' => $this->questionId,
                'pageId' => $this->pageId,
                'position' => $this->postion
            ],
            parent::jsonSerialize()
        );
    }
}
