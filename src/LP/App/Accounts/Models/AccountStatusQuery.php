<?php

namespace <PERSON><PERSON>\LP\App\Accounts\Models;

use Lamano\LP\DB\Models\Base\AccountStatusModelQuery;

class AccountStatusQuery extends AccountStatusModelQuery
{
    public static function findStatusOrStatusHistoryById(int $statusId): AccountStatus|AccountStatusHistory|null
    {
        return self::create()->findOneById($statusId) ?? AccountStatusHistory::query()->findOneById($statusId);
    }
}
