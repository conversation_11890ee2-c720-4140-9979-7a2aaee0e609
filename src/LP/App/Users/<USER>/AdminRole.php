<?php

declare(strict_types=1);

namespace Lamano\LP\App\Users\Models;

enum AdminRole: string
{
    case Guest = '';
    case Admin = 'admin';
    case Accounting = 'accounting';
    case Developer = 'developer';
    case Support = 'support';

    public static function fromUser(User $user): self
    {
        return match (get_class($user)) {
            AdminUser::class => self::Admin,
            AccountingUser::class => self::Accounting,
            DevUser::class => self::Developer,
            HelpDeskUser::class,
            SupportUser::class => self::Support,
            default => self::Guest,
        };
    }
}
