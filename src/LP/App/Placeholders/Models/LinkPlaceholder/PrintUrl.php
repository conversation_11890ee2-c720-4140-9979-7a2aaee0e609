<?php

declare(strict_types=1);

namespace <PERSON>no\LP\App\Placeholders\Models\LinkPlaceholder;

use LAMAPOLL_Response;

class PrintUrl extends Link
{
    public const    PREFIX = 'POLL';
    public const    NAME = 'PRINTOUTURL'; // prefered name would be PRINTURL, but it is already used by LegacyPrintLink.
    public const TYPE = 'url';

    public function __construct()
    {
        parent::__construct('printUrl');
    }

    public function resolve(string $text, LAMAPOLL_Response $response = null): string
    {
        $url = $this->getResponseUrl($response);
        $url .= '&action=print';

        $placeholder = $this->getPlaceholder();
        return str_replace($placeholder, $url, $text);
    }
}
