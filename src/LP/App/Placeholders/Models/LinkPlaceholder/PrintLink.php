<?php

declare(strict_types=1);

namespace Lamano\LP\App\Placeholders\Models\LinkPlaceholder;

use LAMAPOLL_Response;

class PrintLink extends Link
{
    public const    PREFIX = 'POLL';
    public const    NAME = 'PRINTLINK';

    public function __construct()
    {
        parent::__construct('printLink');
    }

    public function resolve(string $text, LAMAPOLL_Response $response = null): string
    {
        $poll = $response->getPoll();
        $url = $this->getResponseUrl($response);
        $url .= '&action=print';

        $link = '<a href="' . $url . '" target="_blank">' . $poll->getName() . '</a>';

        $placeholder = $this->getPlaceholder();
        return str_replace($placeholder, $link, $text);
    }
}
