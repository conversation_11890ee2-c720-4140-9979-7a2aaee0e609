<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\LP\App\Teams;

use <PERSON>no\LP\App\Features\FeaturePolicy;
use <PERSON>no\LP\App\IdentityCard\IdentityCard;
use <PERSON>no\LP\App\Policies\Policy;
use <PERSON>no\LP\App\Policies\PolicyService;
use <PERSON>no\LP\App\Tags\Models\TeamTag;
use Lamano\LP\App\Users\Models\SubUser;

/**
 * Policy for SubUser and TeamTag
 */
final class TeamMemberPolicy extends Policy
{
    protected FeaturePolicy $featurePolicy;
    protected TeamPolicy $teamPolicy;

    public function __construct(
        IdentityCard $identityCard,
        PolicyService $policyService
    ) {
        parent::__construct($identityCard);
        $this->featurePolicy = $policyService->getPolicyForIdentityCard(FeaturePolicy::class, $identityCard);
        $this->teamPolicy = $policyService->getPolicyForIdentityCard(TeamPolicy::class, $identityCard);
    }

    public function canList(): bool
    {
        if (!$this->featurePolicy->hasTeamManagement()) {
            return false;
        }

        return $this->identityCard->isSubUser();
    }

    public function canCreate(): bool
    {
        // not implemented
        return false;
    }

    public function canRead(): bool
    {
        // not implemented
        return false;
    }

    public function canUpdate(SubUser $subUser, TeamTag $team): bool
    {
        if (!$this->teamPolicy->canUpdate($team)) {
            return false;
        }

        return $subUser->getAccountId() === $this->identityCard->getAccountId();
    }

    public function canDelete(SubUser $subUser, TeamTag $team): bool
    {
        return $this->canUpdate($subUser, $team);
    }
}
