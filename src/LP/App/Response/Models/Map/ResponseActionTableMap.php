<?php /** @noinspection ALL */

namespace <PERSON>no\LP\App\Response\Models\Map;

use Lamano\LP\App\Response\Models\ResponseAction;
use Lamano\LP\App\Response\Models\ResponseActionQuery;
use Propel\Runtime\Propel;
use Propel\Runtime\ActiveQuery\Criteria;
use Propel\Runtime\ActiveQuery\InstancePoolTrait;
use Propel\Runtime\Connection\ConnectionInterface;
use Propel\Runtime\DataFetcher\DataFetcherInterface;
use Propel\Runtime\Exception\PropelException;
use Propel\Runtime\Map\RelationMap;
use Propel\Runtime\Map\TableMap;
use Propel\Runtime\Map\TableMapTrait;


/**
 * This class defines the structure of the 'lamapoll_response_actions' table.
 *
 *
 *
 * This map class is used by Propel to do runtime db structure discovery.
 * For example, the createSelectSql() method checks the type of a given column used in an
 * ORDER BY clause to know whether it needs to apply SQL to make the ORDER BY case-insensitive
 * (i.e. if it's a text column type).
 */
class ResponseActionTableMap extends TableMap
{
    use InstancePoolTrait;
    use TableMapTrait;

    /**
     * The (dot-path) name of this class
     */
    public const CLASS_NAME = '../../App/Response/Models/.Map.ResponseActionTableMap';

    /**
     * The default database name for this class
     */
    public const DATABASE_NAME = 'lamapoll';

    /**
     * The table name for this class
     */
    public const TABLE_NAME = 'lamapoll_response_actions';

    /**
     * The PHP name of this class (PascalCase)
     */
    public const TABLE_PHP_NAME = 'ResponseAction';

    /**
     * The related Propel class for this table
     */
    public const OM_CLASS = '\\Lamano\\LP\\App\\Response\\Models\\ResponseAction';

    /**
     * A class that can be returned by this tableMap
     */
    public const CLASS_DEFAULT = '../../App/Response/Models/.ResponseAction';

    /**
     * The total number of columns
     */
    public const NUM_COLUMNS = 6;

    /**
     * The number of lazy-loaded columns
     */
    public const NUM_LAZY_LOAD_COLUMNS = 0;

    /**
     * The number of columns to hydrate (NUM_COLUMNS - NUM_LAZY_LOAD_COLUMNS)
     */
    public const NUM_HYDRATE_COLUMNS = 6;

    /**
     * the column name for the RESPONSE_ID field
     */
    public const COL_RESPONSE_ID = 'lamapoll_response_actions.RESPONSE_ID';

    /**
     * the column name for the POLL_ID field
     */
    public const COL_POLL_ID = 'lamapoll_response_actions.POLL_ID';

    /**
     * the column name for the ACTION_ID field
     */
    public const COL_ACTION_ID = 'lamapoll_response_actions.ACTION_ID';

    /**
     * the column name for the DATA field
     */
    public const COL_DATA = 'lamapoll_response_actions.DATA';

    /**
     * the column name for the ITERATION field
     */
    public const COL_ITERATION = 'lamapoll_response_actions.ITERATION';

    /**
     * the column name for the RESPONSE_ACTION_ID field
     */
    public const COL_RESPONSE_ACTION_ID = 'lamapoll_response_actions.RESPONSE_ACTION_ID';

    /**
     * The default string format for model objects of the related table
     */
    public const DEFAULT_STRING_FORMAT = 'YAML';

    /**
     * holds an array of fieldnames
     *
     * first dimension keys are the type constants
     * e.g. self::$fieldNames[self::TYPE_PHPNAME][0] = 'Id'
     *
     * @var array<string, mixed>
     */
    protected static $fieldNames = [
        self::TYPE_PHPNAME       => ['ResponseId', 'PollId', 'ActionId', 'Data', 'Iteration', 'ResponseActionId', ],
        self::TYPE_CAMELNAME     => ['responseId', 'pollId', 'actionId', 'data', 'iteration', 'responseActionId', ],
        self::TYPE_COLNAME       => [ResponseActionTableMap::COL_RESPONSE_ID, ResponseActionTableMap::COL_POLL_ID, ResponseActionTableMap::COL_ACTION_ID, ResponseActionTableMap::COL_DATA, ResponseActionTableMap::COL_ITERATION, ResponseActionTableMap::COL_RESPONSE_ACTION_ID, ],
        self::TYPE_FIELDNAME     => ['RESPONSE_ID', 'POLL_ID', 'ACTION_ID', 'DATA', 'ITERATION', 'RESPONSE_ACTION_ID', ],
        self::TYPE_NUM           => [0, 1, 2, 3, 4, 5, ]
    ];

    /**
     * holds an array of keys for quick access to the fieldnames array
     *
     * first dimension keys are the type constants
     * e.g. self::$fieldKeys[self::TYPE_PHPNAME]['Id'] = 0
     *
     * @var array<string, mixed>
     */
    protected static $fieldKeys = [
        self::TYPE_PHPNAME       => ['ResponseId' => 0, 'PollId' => 1, 'ActionId' => 2, 'Data' => 3, 'Iteration' => 4, 'ResponseActionId' => 5, ],
        self::TYPE_CAMELNAME     => ['responseId' => 0, 'pollId' => 1, 'actionId' => 2, 'data' => 3, 'iteration' => 4, 'responseActionId' => 5, ],
        self::TYPE_COLNAME       => [ResponseActionTableMap::COL_RESPONSE_ID => 0, ResponseActionTableMap::COL_POLL_ID => 1, ResponseActionTableMap::COL_ACTION_ID => 2, ResponseActionTableMap::COL_DATA => 3, ResponseActionTableMap::COL_ITERATION => 4, ResponseActionTableMap::COL_RESPONSE_ACTION_ID => 5, ],
        self::TYPE_FIELDNAME     => ['RESPONSE_ID' => 0, 'POLL_ID' => 1, 'ACTION_ID' => 2, 'DATA' => 3, 'ITERATION' => 4, 'RESPONSE_ACTION_ID' => 5, ],
        self::TYPE_NUM           => [0, 1, 2, 3, 4, 5, ]
    ];

    /**
     * Holds a list of column names and their normalized version.
     *
     * @var array<string>
     */
    protected $normalizedColumnNameMap = [
        'ResponseId' => 'RESPONSE_ID',
        'ResponseAction.ResponseId' => 'RESPONSE_ID',
        'responseId' => 'RESPONSE_ID',
        'responseAction.responseId' => 'RESPONSE_ID',
        'ResponseActionTableMap::COL_RESPONSE_ID' => 'RESPONSE_ID',
        'COL_RESPONSE_ID' => 'RESPONSE_ID',
        'RESPONSE_ID' => 'RESPONSE_ID',
        'lamapoll_response_actions.RESPONSE_ID' => 'RESPONSE_ID',
        'PollId' => 'POLL_ID',
        'ResponseAction.PollId' => 'POLL_ID',
        'pollId' => 'POLL_ID',
        'responseAction.pollId' => 'POLL_ID',
        'ResponseActionTableMap::COL_POLL_ID' => 'POLL_ID',
        'COL_POLL_ID' => 'POLL_ID',
        'POLL_ID' => 'POLL_ID',
        'lamapoll_response_actions.POLL_ID' => 'POLL_ID',
        'ActionId' => 'ACTION_ID',
        'ResponseAction.ActionId' => 'ACTION_ID',
        'actionId' => 'ACTION_ID',
        'responseAction.actionId' => 'ACTION_ID',
        'ResponseActionTableMap::COL_ACTION_ID' => 'ACTION_ID',
        'COL_ACTION_ID' => 'ACTION_ID',
        'ACTION_ID' => 'ACTION_ID',
        'lamapoll_response_actions.ACTION_ID' => 'ACTION_ID',
        'Data' => 'DATA',
        'ResponseAction.Data' => 'DATA',
        'data' => 'DATA',
        'responseAction.data' => 'DATA',
        'ResponseActionTableMap::COL_DATA' => 'DATA',
        'COL_DATA' => 'DATA',
        'DATA' => 'DATA',
        'lamapoll_response_actions.DATA' => 'DATA',
        'Iteration' => 'ITERATION',
        'ResponseAction.Iteration' => 'ITERATION',
        'iteration' => 'ITERATION',
        'responseAction.iteration' => 'ITERATION',
        'ResponseActionTableMap::COL_ITERATION' => 'ITERATION',
        'COL_ITERATION' => 'ITERATION',
        'ITERATION' => 'ITERATION',
        'lamapoll_response_actions.ITERATION' => 'ITERATION',
        'ResponseActionId' => 'RESPONSE_ACTION_ID',
        'ResponseAction.ResponseActionId' => 'RESPONSE_ACTION_ID',
        'responseActionId' => 'RESPONSE_ACTION_ID',
        'responseAction.responseActionId' => 'RESPONSE_ACTION_ID',
        'ResponseActionTableMap::COL_RESPONSE_ACTION_ID' => 'RESPONSE_ACTION_ID',
        'COL_RESPONSE_ACTION_ID' => 'RESPONSE_ACTION_ID',
        'RESPONSE_ACTION_ID' => 'RESPONSE_ACTION_ID',
        'lamapoll_response_actions.RESPONSE_ACTION_ID' => 'RESPONSE_ACTION_ID',
    ];

    /**
     * Initialize the table attributes and columns
     * Relations are not initialized by this method since they are lazy loaded
     *
     * @return void
     * @throws \Propel\Runtime\Exception\PropelException
     */
    public function initialize(): void
    {
        // attributes
        $this->setName('lamapoll_response_actions');
        $this->setPhpName('ResponseAction');
        $this->setIdentifierQuoting(true);
        $this->setClassName('\\Lamano\\LP\\App\\Response\\Models\\ResponseAction');
        $this->setPackage('../../App/Response/Models/');
        $this->setUseIdGenerator(true);
        // columns
        $this->addColumn('RESPONSE_ID', 'ResponseId', 'CHAR', true, 6, null);
        $this->addColumn('POLL_ID', 'PollId', 'INTEGER', true, null, null);
        $this->addColumn('ACTION_ID', 'ActionId', 'INTEGER', true, null, null);
        $this->addColumn('DATA', 'Data', 'LONGVARCHAR', true, null, null);
        $this->addColumn('ITERATION', 'Iteration', 'SMALLINT', true, null, null);
        $this->addPrimaryKey('RESPONSE_ACTION_ID', 'ResponseActionId', 'INTEGER', true, null, null);
    }

    /**
     * Build the RelationMap objects for this table relationships
     *
     * @return void
     */
    public function buildRelations(): void
    {
    }

    /**
     * Retrieves a string version of the primary key from the DB resultset row that can be used to uniquely identify a row in this table.
     *
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, a serialize()d version of the primary key will be returned.
     *
     * @param array $row Resultset row.
     * @param int $offset The 0-based offset for reading from the resultset row.
     * @param string $indexType One of the class type constants TableMap::TYPE_PHPNAME, TableMap::TYPE_CAMELNAME
     *                           TableMap::TYPE_COLNAME, TableMap::TYPE_FIELDNAME, TableMap::TYPE_NUM
     *
     * @return string|null The primary key hash of the row
     */
    public static function getPrimaryKeyHashFromRow(array $row, int $offset = 0, string $indexType = TableMap::TYPE_NUM): ?string
    {
        // If the PK cannot be derived from the row, return NULL.
        if ($row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)] === null) {
            return null;
        }

        return null === $row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)] || is_scalar($row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)]) || is_callable([$row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)], '__toString']) ? (string) $row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)] : $row[TableMap::TYPE_NUM == $indexType ? 5 + $offset : static::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)];
    }

    /**
     * Retrieves the primary key from the DB resultset row
     * For tables with a single-column primary key, that simple pkey value will be returned.  For tables with
     * a multi-column primary key, an array of the primary key columns will be returned.
     *
     * @param array $row Resultset row.
     * @param int $offset The 0-based offset for reading from the resultset row.
     * @param string $indexType One of the class type constants TableMap::TYPE_PHPNAME, TableMap::TYPE_CAMELNAME
     *                           TableMap::TYPE_COLNAME, TableMap::TYPE_FIELDNAME, TableMap::TYPE_NUM
     *
     * @return mixed The primary key of the row
     */
    public static function getPrimaryKeyFromRow(array $row, int $offset = 0, string $indexType = TableMap::TYPE_NUM)
    {
        return (int) $row[
            $indexType == TableMap::TYPE_NUM
                ? 5 + $offset
                : self::translateFieldName('ResponseActionId', TableMap::TYPE_PHPNAME, $indexType)
        ];
    }

    /**
     * The class that the tableMap will make instances of.
     *
     * If $withPrefix is true, the returned path
     * uses a dot-path notation which is translated into a path
     * relative to a location on the PHP include_path.
     * (e.g. path.to.MyClass -> 'path/to/MyClass.php')
     *
     * @param bool $withPrefix Whether to return the path with the class name
     * @return string path.to.ClassName
     */
    public static function getOMClass(bool $withPrefix = true): string
    {
        return $withPrefix ? ResponseActionTableMap::CLASS_DEFAULT : ResponseActionTableMap::OM_CLASS;
    }

    /**
     * Populates an object of the default type or an object that inherit from the default.
     *
     * @param array $row Row returned by DataFetcher->fetch().
     * @param int $offset The 0-based offset for reading from the resultset row.
     * @param string $indexType The index type of $row. Mostly DataFetcher->getIndexType().
                                 One of the class type constants TableMap::TYPE_PHPNAME, TableMap::TYPE_CAMELNAME
     *                           TableMap::TYPE_COLNAME, TableMap::TYPE_FIELDNAME, TableMap::TYPE_NUM.
     *
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     * @return array (ResponseAction object, last column rank)
     */
    public static function populateObject(array $row, int $offset = 0, string $indexType = TableMap::TYPE_NUM): array
    {
        $key = ResponseActionTableMap::getPrimaryKeyHashFromRow($row, $offset, $indexType);
        if (null !== ($obj = ResponseActionTableMap::getInstanceFromPool($key))) {
            // We no longer rehydrate the object, since this can cause data loss.
            // See http://www.propelorm.org/ticket/509
            // $obj->hydrate($row, $offset, true); // rehydrate
            $col = $offset + ResponseActionTableMap::NUM_HYDRATE_COLUMNS;
        } else {
            $cls = ResponseActionTableMap::OM_CLASS;
            /** @var ResponseAction $obj */
            $obj = new $cls();
            $col = $obj->hydrate($row, $offset, false, $indexType);
            ResponseActionTableMap::addInstanceToPool($obj, $key);
        }

        return [$obj, $col];
    }

    /**
     * The returned array will contain objects of the default type or
     * objects that inherit from the default.
     *
     * @param DataFetcherInterface $dataFetcher
     * @return array<object>
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     */
    public static function populateObjects(DataFetcherInterface $dataFetcher): array
    {
        $results = [];

        // set the class once to avoid overhead in the loop
        $cls = static::getOMClass(false);
        // populate the object(s)
        while ($row = $dataFetcher->fetch()) {
            $key = ResponseActionTableMap::getPrimaryKeyHashFromRow($row, 0, $dataFetcher->getIndexType());
            if (null !== ($obj = ResponseActionTableMap::getInstanceFromPool($key))) {
                // We no longer rehydrate the object, since this can cause data loss.
                // See http://www.propelorm.org/ticket/509
                // $obj->hydrate($row, 0, true); // rehydrate
                $results[] = $obj;
            } else {
                /** @var ResponseAction $obj */
                $obj = new $cls();
                $obj->hydrate($row);
                $results[] = $obj;
                ResponseActionTableMap::addInstanceToPool($obj, $key);
            } // if key exists
        }

        return $results;
    }
    /**
     * Add all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be added to the select list and only loaded
     * on demand.
     *
     * @param Criteria $criteria Object containing the columns to add.
     * @param string|null $alias Optional table alias
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     * @return void
     */
    public static function addSelectColumns(Criteria $criteria, ?string $alias = null): void
    {
        if (null === $alias) {
            $criteria->addSelectColumn(ResponseActionTableMap::COL_RESPONSE_ID);
            $criteria->addSelectColumn(ResponseActionTableMap::COL_POLL_ID);
            $criteria->addSelectColumn(ResponseActionTableMap::COL_ACTION_ID);
            $criteria->addSelectColumn(ResponseActionTableMap::COL_DATA);
            $criteria->addSelectColumn(ResponseActionTableMap::COL_ITERATION);
            $criteria->addSelectColumn(ResponseActionTableMap::COL_RESPONSE_ACTION_ID);
        } else {
            $criteria->addSelectColumn($alias . '.RESPONSE_ID');
            $criteria->addSelectColumn($alias . '.POLL_ID');
            $criteria->addSelectColumn($alias . '.ACTION_ID');
            $criteria->addSelectColumn($alias . '.DATA');
            $criteria->addSelectColumn($alias . '.ITERATION');
            $criteria->addSelectColumn($alias . '.RESPONSE_ACTION_ID');
        }
    }

    /**
     * Remove all the columns needed to create a new object.
     *
     * Note: any columns that were marked with lazyLoad="true" in the
     * XML schema will not be removed as they are only loaded on demand.
     *
     * @param Criteria $criteria Object containing the columns to remove.
     * @param string|null $alias Optional table alias
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     * @return void
     */
    public static function removeSelectColumns(Criteria $criteria, ?string $alias = null): void
    {
        if (null === $alias) {
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_RESPONSE_ID);
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_POLL_ID);
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_ACTION_ID);
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_DATA);
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_ITERATION);
            $criteria->removeSelectColumn(ResponseActionTableMap::COL_RESPONSE_ACTION_ID);
        } else {
            $criteria->removeSelectColumn($alias . '.RESPONSE_ID');
            $criteria->removeSelectColumn($alias . '.POLL_ID');
            $criteria->removeSelectColumn($alias . '.ACTION_ID');
            $criteria->removeSelectColumn($alias . '.DATA');
            $criteria->removeSelectColumn($alias . '.ITERATION');
            $criteria->removeSelectColumn($alias . '.RESPONSE_ACTION_ID');
        }
    }

    /**
     * Returns the TableMap related to this object.
     * This method is not needed for general use but a specific application could have a need.
     * @return TableMap
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     */
    public static function getTableMap(): TableMap
    {
        return Propel::getServiceContainer()->getDatabaseMap(ResponseActionTableMap::DATABASE_NAME)->getTable(ResponseActionTableMap::TABLE_NAME);
    }

    /**
     * Performs a DELETE on the database, given a ResponseAction or Criteria object OR a primary key value.
     *
     * @param mixed $values Criteria or ResponseAction object or primary key or array of primary keys
     *              which is used to create the DELETE statement
     * @param ConnectionInterface $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).  This includes CASCADE-related rows
     *                         if supported by native driver or if emulated using Propel.
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     */
     public static function doDelete($values, ?ConnectionInterface $con = null): int
     {
        if (null === $con) {
            $con = Propel::getServiceContainer()->getWriteConnection(ResponseActionTableMap::DATABASE_NAME);
        }

        if ($values instanceof Criteria) {
            // rename for clarity
            $criteria = $values;
        } elseif ($values instanceof \Lamano\LP\App\Response\Models\ResponseAction) { // it's a model object
            // create criteria based on pk values
            $criteria = $values->buildPkeyCriteria();
        } else { // it's a primary key, or an array of pks
            $criteria = new Criteria(ResponseActionTableMap::DATABASE_NAME);
            $criteria->add(ResponseActionTableMap::COL_RESPONSE_ACTION_ID, (array) $values, Criteria::IN);
        }

        $query = ResponseActionQuery::create()->mergeWith($criteria);

        if ($values instanceof Criteria) {
            ResponseActionTableMap::clearInstancePool();
        } elseif (!is_object($values)) { // it's a primary key, or an array of pks
            foreach ((array) $values as $singleval) {
                ResponseActionTableMap::removeInstanceFromPool($singleval);
            }
        }

        return $query->delete($con);
    }

    /**
     * Deletes all rows from the lamapoll_response_actions table.
     *
     * @param ConnectionInterface $con the connection to use
     * @return int The number of affected rows (if supported by underlying database driver).
     */
    public static function doDeleteAll(?ConnectionInterface $con = null): int
    {
        return ResponseActionQuery::create()->doDeleteAll($con);
    }

    /**
     * Performs an INSERT on the database, given a ResponseAction or Criteria object.
     *
     * @param mixed $criteria Criteria or ResponseAction object containing data that is used to create the INSERT statement.
     * @param ConnectionInterface $con the ConnectionInterface connection to use
     * @return mixed The new primary key.
     * @throws \Propel\Runtime\Exception\PropelException Any exceptions caught during processing will be
     *                         rethrown wrapped into a PropelException.
     */
    public static function doInsert($criteria, ?ConnectionInterface $con = null)
    {
        if (null === $con) {
            $con = Propel::getServiceContainer()->getWriteConnection(ResponseActionTableMap::DATABASE_NAME);
        }

        if ($criteria instanceof Criteria) {
            $criteria = clone $criteria; // rename for clarity
        } else {
            $criteria = $criteria->buildCriteria(); // build Criteria from ResponseAction object
        }

        if ($criteria->containsKey(ResponseActionTableMap::COL_RESPONSE_ACTION_ID) && $criteria->keyContainsValue(ResponseActionTableMap::COL_RESPONSE_ACTION_ID) ) {
            throw new PropelException('Cannot insert a value for auto-increment primary key ('.ResponseActionTableMap::COL_RESPONSE_ACTION_ID.')');
        }


        // Set the correct dbName
        $query = ResponseActionQuery::create()->mergeWith($criteria);

        // use transaction because $criteria could contain info
        // for more than one table (I guess, conceivably)
        return $con->transaction(function () use ($con, $query) {
            return $query->doInsert($con);
        });
    }

}
