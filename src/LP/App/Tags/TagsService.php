<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\LP\App\Tags;

use Exception;
use <PERSON><PERSON>\LP\App\Tags\Models\CategoryTag;
use Lamano\LP\App\Tags\Models\Tag;
use Lamano\LP\App\Tags\Models\TeamTag;
use Lamano\LP\App\Tags\Models\UserTag;

class TagsService
{
    /**
     * @throws Exception
     */
    public function isSubUserCategory(CategoryTag $tag): bool
    {
        return $tag->getTopParent() instanceof UserTag;
    }

    public function isAccountCategory(CategoryTag $category): bool
    {
        $topParent = $category->getTopParent();

        return $topParent instanceof CategoryTag &&
            $topParent->getParentId() === 0;
    }

    public function isTeamCategory(CategoryTag $category): bool
    {
        $topParent = $category->getTopParent();
        if ($topParent instanceof TeamTag) {
            return true;
        }

        return false;
    }

    /**
     * finds the parent tag, that a child-tag is bound to (in subuser-context)
     * bound parent can be:
     *  - a UserTag or
     *  - a TeamTag or
     *  - a shared CategoryTag or
     *  - the account root (=0)
     */
    public function getBoundParent(Tag $tag): ?Tag
    {
        if ($tag instanceof CategoryTag) {
            if ($tag->isShared()) {
                return $tag;
            }

            if ($tag->hasSharedParent()) {
                return $tag->getSharedParent();
            }

            $topParent = $tag->getTopParent();
            if (
                $topParent instanceof UserTag ||
                $topParent instanceof TeamTag
            ) {
                return $topParent;
            }
        }

        if (
            $tag instanceof TeamTag ||
            $tag instanceof UserTag
        ) {
            return $tag;
        }

        return null;
    }

    public function sortByName(Tag $a, Tag $b): int
    {
        return strnatcasecmp($a->getLongDisplayName(), $b->getLongDisplayName());
    }
}
