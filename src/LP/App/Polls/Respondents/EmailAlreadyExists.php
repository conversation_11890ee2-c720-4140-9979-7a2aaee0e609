<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\LP\App\Polls\Respondents;

use <PERSON>no\LP\App\APIException;
use Lamano\LP\Core\Response\HttpResponse;

/**
 * The email address that claims to be unique is already associated with a respondent.
 *
 * @extends APIException<array{email: string}>
 */
class EmailAlreadyExists extends APIException
{
    public const KEY = 'EMAIL_ALREADY_EXISTS';

    public function __construct(public readonly string $email)
    {
        parent::__construct("Email '{$email}' already exists", HttpResponse::CONFLICT);
    }

    public function jsonSerialize(): array
    {
        return [
            'error' => self::KEY,
            'message' => $this->getMessage(),
            'details' => [
                'email' => $this->email,
            ],
        ];
    }
}
