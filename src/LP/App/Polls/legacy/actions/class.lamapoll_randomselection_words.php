<?php

class LAMAPOLL_RandomSelection_Words extends LAMAPOLL_RandomSelection
{
    // ----------------------------- //
    // Action Methods Accessors
    // ----------------------------- //

    public function getActionDetails($mode = 'html', $short = false)
    {
        $br = $mode == 'html' ? HTML_BR : ' ; ';
        $details = '';
        if (!$short) {
            $details .= lp_get_text('RS_NUM_VALUES') . ': ' . $this->getNumWords() . $br;
            $details .= lp_get_text('RS_NUM_SELECTIONS') . ': ' . $this->getNumDraws() . $br;
        } else {
            $details .= $this->getNumDraws() . 'x ' . lp_get_text('WORD_OF') . ' ' . $this->getNumWords(
                ) . ' ' . lp_get_text('WORDS');
        }
        return $details;
    }

    // ----------------------------- //
    // Candidates Accessors
    // ----------------------------- //
    public function hasWord($word)
    {
        foreach ($this->_settings['words'] as $cWord) {
            if ($cWord['word'] == $word) {
                return true;
            }
        }
        return false;
    }

    public function getWord($word)
    {
        foreach ($this->_settings['words'] as $cWord) {
            if ($cWord['word'] == $word) {
                return $cWord;
            }
        }
        return false;
    }

    public function getNumWords()
    {
        return count($this->_settings['words'] ?? []);
    }

    public function getWordList()
    {
        return $this->_settings['words'];
    }

    public function setWordList($newWords)
    {
        $oldWords = $this->_settings['words'] ?? [];

        // detect obsolete filters by collecting all old filters
        // and removing then all filters, that are still in use
        $obsoleteFilters = [];
        if (is_array($oldWords) && !empty($oldWords)) {
            foreach ($oldWords as $word) {
                $obsoleteFilters[$word['filterId']] = true;
            }
        }

        foreach ($newWords as $word) {
            unset($obsoleteFilters[$word['filterId']]);
        }

        // now $obsoleteFilters contains filters, that can be deleted
        foreach (array_keys($obsoleteFilters) as $filterId) {
            LAMAPOLL_Filter::deleteFilterById($filterId);
        }

        $this->_settings['words'] = $newWords;
    }

    // ------------------------------ //
    // RandomSelection Implementation //
    // ------------------------------ //

    public function supportsQuotaFilter()
    {
        return true;
    }

    protected function doRandomizeAction(LAMAPOLL_Response $response, $answers)
    {
        $words = $this->getWordList();
        $candidatesList = [];
        foreach ($words as $wId => $word) {
            $prio = intval($word['priority']);
            $weight = intval($word['weight']);
            $filter = is_numeric($word['filterId']) ? LAMAPOLL_Filter::getFilterById($word['filterId']) : false;

            $isCandidate = true;
            if ($filter instanceof LAMAPOLL_Filter) {
                $evalTerms = [];
                $isCandidate = $filter->evaluateAnswers($response, $answers, $evalTerms);
            }

            if ($isCandidate) {
                $candidatesList[$prio][$wId] = $weight > 0 ? $weight : 1;
            }
        }

        krsort($candidatesList);

        $selected = $this->_selectCandidates($this->getNumDraws(), $candidatesList);

        $results = [];
        foreach ($selected as $wId) {
            $results[] = $words[$wId];
        }

        return $results;
    }

    protected function getRandomResult($results, $item, $asText = true)
    {
        if (empty($results) || !is_array($results)) {
            return '';
        }
        $wordIndex = $item - 1;

        if (isset($results[$wordIndex])) {
            return $results[$wordIndex]['word'];
        }

        return '';
    }

    protected function validateActionResults(LAMAPOLL_Response $response, $answers, $actionsResults)
    {
        // Check all randomized candidates if they are in good condition
        // or if they are changed (not selected/filtered out) for examples
        if (!is_array($actionsResults)) {
            return false;
        }

        foreach ($actionsResults as $resultWord) {
            // valid candidate?
            if (!$this->hasWord($resultWord['word'])) {
                return false;
            }
            $wordItem = $this->getWord($resultWord['word']);

            // Check if filter is applied
            $filter = is_numeric($wordItem['filterId']) ? LAMAPOLL_Filter::getFilterById($wordItem['filterId']) : false;

            $isCandidate = true;
            if ($filter instanceof LAMAPOLL_Filter) {
                $evalTerms = [];
                $isCandidate = $filter->evaluateAnswers($response, $answers, $evalTerms);
            }

            if (!$isCandidate) {
                return false;
            }
        }
        return true;
    }

    protected function validateSettings(LAMAPOLL_Poll $poll): ?array
    {
        $errors = null;
        foreach ($this->getWordList() as $i => $word) {
            if (is_numeric($word['filterId'])) {
                $filter = LAMAPOLL_Filter::getFilterById($word['filterId']);
                if ($filter instanceof LAMAPOLL_Filter) {
                    $errs = $filter->validate($poll, LAMAPOLL_Filter::KIND_ACTION);
                    if ($errs != null) {
                        $errors[$i + 1] = current($errs['errors']); // key($errs['errors']) => which term failed
                    }
                } else {
                    $errors[$i + 1] = 'POLLACTION_NO_FILTER_FOR_ACTION';
                }
            }
        }
        return $errors ? ['errors' => $errors] : null;
    }

    protected function cloneItems($newPollId, $newPageIds, $newQuestionIds, $newSelectorIds)
    {
        // NOTHING TO DO HERE
    }

    protected function setupCloneItems($newPollId, $newPageIds, $newQuestionIds, $newSelectorIds, $newActionIds)
    {
        foreach ($this->getWordList() as $i => $word) {
            $filterId = $word['filterId'];
            if ($filterId && is_numeric($filterId)) {
                $filter = LAMAPOLL_Filter::getFilterById($filterId);
                if ($filter instanceof LAMAPOLL_Filter) {
                    $this->_settings['words'][$i]['filterId'] = $filter->createClone(
                        $newPollId,
                        $newPageIds,
                        $newQuestionIds,
                        $newSelectorIds,
                        $newActionIds
                    );
                } else {
                    $this->_settings['words'][$i]['filterId'] = 0; // clear for safety for missing filters
                }
            }
        }
    }


    private function _selectCandidates($numSelections, $candidates)
    {
        $result = [];
        $numChosen = 0;
        $numLeft = $numSelections;

        // Candiates (Id=>Gewicht):
        //    C1=>4 , C2=>2 , C3=>1
        //

        foreach ($candidates as $prio => $cands) {
            $sum = array_sum($cands); // Sum all weights == Anzahl Items

            while ($numLeft > 0 && !empty($cands)) {
                $randomIndex = mt_rand(0, $sum - 1);
                $cur = 0;
                $selected = -1;
                foreach ($cands as $cId => $weight) {
                    $cur += $weight;
                    if ($randomIndex < $cur) {
                        $selected = $cId;
                        break;
                    }
                }
                $numLeft--;

                if ($selected != -1) {
                    $sum -= $weight;
                    $result[] = $selected;
                    unset($cands[$selected]);
                }
            }
            if ($numLeft == 0) {
                break;
            }
        }

        return $result;
    }
}
