<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\LP\App\Polls\Questions\Models\Ranking;

use Lamano\LP\DB\Models\Base\BaseRankingQuestionFactory;

class RankingQuestionFactory extends BaseRankingQuestionFactory
{
    protected function definition(): array
    {
        return [
            'rankingType' => $this->faker->word(),
            'ignoreGroups' => $this->faker->word(),
        ];
    }
}
