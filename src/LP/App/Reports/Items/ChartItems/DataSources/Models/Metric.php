<?php

declare(strict_types=1);

namespace <PERSON>no\LP\App\Reports\Items\ChartItems\DataSources\Models;

use JsonSerializable;
use Lamano\LP\App\Reports\Models\FrequencyType;
use Lamano\LP\App\Results\QuestionResults\StatisticsType;
use Lamano\LP\App\Results\QuestionResults\SummaryType;

class Metric implements JsonSerializable
{
    protected MetricCategory $category;
    protected FrequencyType|StatisticsType|SummaryType $type;

    public function __construct(
        MetricCategory|string $category = MetricCategory::Frequency,
        FrequencyType|StatisticsType|SummaryType|string $type = FrequencyType::Absolute,
        protected ?string $key = null,
    ) {
        $this->setCategory($category);
        $this->setType($type);
    }

    protected function setCategory(MetricCategory|string $category): void
    {
        $this->category = $category instanceof MetricCategory ?
            $category : MetricCategory::from($category);
    }

    public function getCategory(): MetricCategory
    {
        return $this->category;
    }

    public function getType(): FrequencyType|StatisticsType|SummaryType
    {
        return $this->type;
    }

    public function getKey(): ?string
    {
        return $this->key;
    }

    protected function setType(FrequencyType|StatisticsType|SummaryType|string $type): void
    {
        $this->type = match ($this->category) {
            MetricCategory::Frequency => $type instanceof FrequencyType ?
                $type : FrequencyType::from($type),
            MetricCategory::Stats => $type instanceof StatisticsType ?
                $type : StatisticsType::from($type),
            MetricCategory::Summary => $type instanceof SummaryType ?
                $type : SummaryType::from($type)
        };
    }

    public function update(
        MetricCategory|string|null $category = null,
        FrequencyType|StatisticsType|SummaryType|string|null $type = null,
        string|null $key = null,
    ): void {
        if ($category !== null) {
            $this->setCategory($category);
            $this->key = null;
        }

        if ($type !== null) {
            $this->setType($type);
        }

        if ($key !== null) {
            $this->key = $key;
        }
    }

    public function jsonSerialize(): array
    {
        return [
            MetricKeys::Category->value => $this->category,
            MetricKeys::Type->value => $this->type,
            MetricKeys::Key->value => $this->key,
        ];
    }
}
