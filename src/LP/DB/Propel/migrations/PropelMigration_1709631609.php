<?php

use Propel\Generator\Manager\MigrationManager;

/**
 * Data object containing the SQL and PHP code to migrate the database
 * up to version 1709631609.
 * Generated on 2024-03-05 09:40:09 by www-data */
class PropelMigration_1709631609
{
    /**
     * @var string
     */
    public $comment = '';

    /**
     * @param \Propel\Generator\Manager\MigrationManager $manager
     *
     * @return null|false|void
     */
    public function preUp(MigrationManager $manager)
    {
        // add the pre-migration code here
    }

    /**
     * @param \Propel\Generator\Manager\MigrationManager $manager
     *
     * @return null|false|void
     */
    public function postUp(MigrationManager $manager)
    {
        // add the post-migration code here
    }

    /**
     * @param \Propel\Generator\Manager\MigrationManager $manager
     *
     * @return null|false|void
     */
    public function preDown(MigrationManager $manager)
    {
        // add the pre-migration code here
    }

    /**
     * @param \Propel\Generator\Manager\MigrationManager $manager
     *
     * @return null|false|void
     */
    public function postDown(MigrationManager $manager)
    {
        // add the post-migration code here
    }

    /**
     * Get the SQL statements for the Up migration
     *
     * @return array list of the SQL strings to execute for the Up migration
     *               the keys being the datasources
     */
    public function getUpSQL(): array
    {
        $connection_lamapoll = <<< 'EOT'
            DELETE FROM lamapoll_respondents
            WHERE NOT EXISTS (
              SELECT 1 FROM lamapoll_respondents_responses
              WHERE lamapoll_respondents.ID = lamapoll_respondents_responses.RESPONDENT_ID
            )
EOT;

        $connection_lamapoll_user_logs = <<< 'EOT'
EOT;

        $connection_lamapoll_sessions = <<< 'EOT'
EOT;

        return [
            'lamapoll' => $connection_lamapoll,
            'lamapoll_user_logs' => $connection_lamapoll_user_logs,
            'lamapoll_sessions' => $connection_lamapoll_sessions,
        ];
    }

    /**
     * Get the SQL statements for the Down migration
     *
     * @return array list of the SQL strings to execute for the Down migration
     *               the keys being the datasources
     */
    public function getDownSQL(): array
    {
        $connection_lamapoll = <<< 'EOT'
EOT;

        $connection_lamapoll_user_logs = <<< 'EOT'
EOT;

        $connection_lamapoll_sessions = <<< 'EOT'
EOT;

        return [
            'lamapoll' => $connection_lamapoll,
            'lamapoll_user_logs' => $connection_lamapoll_user_logs,
            'lamapoll_sessions' => $connection_lamapoll_sessions,
        ];
    }

}
