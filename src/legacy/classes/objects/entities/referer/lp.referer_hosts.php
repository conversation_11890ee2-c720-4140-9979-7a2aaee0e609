<?php

class LP_Referer_Hosts extends LP_Entity_List
{
    private static $_instance = null;

    const DB_TABLE = 'referer_hosts';
    const DB_FIELD_ID = 'HOST_ID';
    const DB_FIELD_IDENT = 'HOST';

    const EMPTY_HOST = 1;

    const MAX_HOST_LENGTH = 256; // 255 = maximum hostname length defined by RFC, often even shorter in reality

    protected static function getInstance(): LP_Referer_Hosts
    {
        if (null === self::$_instance) {
            self::$_instance = new LP_Referer_Hosts();
        }
        return self::$_instance;
    }

    // -------------------- //
    // Entity Methods      //
    // -------------------- //
    public static function addEntity(string $name): int
    {
        return self::getInstance()->add($name);
    }

    protected function add(string $host): int
    {
        $host = substr($host, 0, self::MAX_HOST_LENGTH);

        $existingId = $this->find($host);
        if ($existingId !== false && !is_null($existingId)) {
            return $existingId;
        }

        return $this->insert($host);
    }

    public static function getHostById(int $hostId)
    {
        $host = self::getInstance()->get($hostId);
        return $host ? $host[self::DB_FIELD_IDENT] : '';
    }

    public static function getHostsForPoll(int $pollId)
    {
        $hosts = [
            self::EMPTY_HOST => lp_get_text('NO_SOURCE_LINK'),
        ];
        if ($pollId) {
            // get distinct categories from the view, and join them with the responses so we only get currently used categories for the given poll
            $sql = 'SELECT DISTINCT lri.' . qI(self::DB_FIELD_ID) . ', lri.' . qI(self::DB_FIELD_IDENT);
            $sql .= ' FROM ' . qT(LP_Referers::DB_VIEW) . ' lri ';
            $sql .= ' INNER JOIN ' . qT(LAMAPOLL_Response::DB_TABLE) . ' lr ';
            $sql .= ' ON lr.' . qI(LAMAPOLL_Response::DBFIELD_REFERER_ID) . ' = lri.' . qI(LP_Referers::DB_FIELD_ID);
            $sql .= ' AND lr.' . qI(LAMAPOLL_Response::DBFIELD_POLL_ID) . ' = ' . qV($pollId);
            $sql .= ' AND lr.' . qI(LAMAPOLL_Response::DBFIELD_REFERER_ID) . ' != ' . qV(LP_Referers::UNKNOWN_REFERER);

            $hosts += env()->db()->queryArray($sql);
        }

        return $hosts;
    }

    // --------------------- //
    // Database Requirements //
    // --------------------- //

    protected function getTable(): string
    {
        return self::DB_TABLE;
    }

    protected function getIdField(): string
    {
        return self::DB_FIELD_ID;
    }

    protected function getNameField(): string
    {
        return self::DB_FIELD_IDENT;
    }

    protected function getHashField(): string
    {
        return '';
    }

    protected function getHashAlgorithm(): string
    {
        return '';
    }
}
