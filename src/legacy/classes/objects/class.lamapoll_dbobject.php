<?php

abstract class LAMAPOLL_DBObject extends LAMAPOLL_Object
{
    /**
     * id of object @var int
     */
    protected $_id = 0;

    protected $_dbTable = '';
    protected $_dbField = '';

    private $_inDB = false;

    public function __construct($dbTable, $dbField)
    {
        $this->_dbTable = $dbTable;
        $this->_dbField = $dbField;
    }

    /**
     * returns the object id
     *
     * @return int
     */
    public function getId()
    {
        return $this->_id;
    }

    /**
     * sets id of this item
     *
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->_id = $id;
    }

    public function hasNewId()
    {
        return !LAMAPOLL_Object::isValidId($this->_id) || LAMAPOLL_Object::isNewId($this->_id);
    }

    // --------------------- //
    // Abstract Methods     //
    // --------------------- //

    /**
     * Hint object status (hint only!)
     */
    protected function _getObjectState()
    {
        return $this->_inDB;
    }

    protected function _setObjectState($inDB = true)
    {
        $this->_inDB = $inDB;
        return true;
    }

    /**
     *  get internal object db id
     */
    protected function getObjectId()
    {
        return $this->getId();
    }

    /**
     *  get internal object db table
     */
    protected function getObjectTable()
    {
        return $this->_dbTable;
    }

    /**
     *
     */
    protected function getObjectField()
    {
        return $this->_dbField;
    }

    /**
     * get internal object db where clause (as DB-Field => DB-Value array)
     */
    protected function getObjectWhere()
    {
        return [$this->getObjectField() => $this->getObjectId()];
    }

    /**
     * update an field of the object
     *
     * @param mixed $field
     * @param mixed $value
     */
    protected function updateObjectField($field, $value = 0)
    {
        if ($field && $this->_inDB && $this->getObjectTable() && $this->getObjectId()) {
            return env()->db()->updateData(
                $this->getObjectTable(),
                is_array($field) ? $field : [$field => $value],
                $this->getObjectWhere(),
                1
            );
        } else {
            return false;
        }
    }


    /**
     * Test if object in db
     */
    public function existsInDB()
    {
        // not exists if : an invalid id, or an new id
        if (!LAMAPOLL_Object::isValidId($this->getObjectId()) || LAMAPOLL_Object::isNewId($this->getObjectId())) {
            return false;
        } else { // else exists if :
            //  hinted that is in db (created or loaded and not deleted)
            //  or really exists at least one record in db
            return $this->_inDB || env()->db()->count($this->getObjectTable(), $this->getObjectWhere()) >= 1;
        }
    }


    // abstract public function createInDB(); // not compatible for all objects right now
    // abstract public function saveToDB();   // not campatible for all objects right now
    abstract public function deleteFromDB();

    abstract public function loadFromDB($id);

    abstract protected function loadFromData($data);

    // Cannot declare private on abstract functions - but this function MUST be private
    // It MUST really be private else the parent functions is called instead, which is not desired for getting the db fields at the current object
    // abstract private function getDatabaseFields();
}
