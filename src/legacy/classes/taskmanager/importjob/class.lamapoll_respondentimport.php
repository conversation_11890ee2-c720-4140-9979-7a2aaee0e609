<?php

use <PERSON>no\Core\Errors\Handlers\Sentry\SentryErrorHandler;
use Lamano\Core\Sanitizers\EmailSanitizer;
use Lama<PERSON>\LP\App\Accounts\Models\Account;
use <PERSON>no\LP\App\Features\FeaturePolicy;
use <PERSON>no\LP\App\IdentityCard\IdentityCard;
use <PERSON>no\LP\App\Keypool\KeypoolController;
use Lamano\LP\App\Logging\Models\LogMessageId;
use Lamano\LP\App\Logging\Models\StoredLogger;
use Lamano\LP\App\Policies\PolicyService;
use Lamano\LP\App\Polls\Respondents\Models\Respondent;
use <PERSON>no\LP\App\Polls\Respondents\RespondentsController;
use Lamano\LP\App\Polls\Respondents\RespondentsGateway;
use Lamano\LP\App\Users\Models\User;
use Lamano\LP\Core\Security\RequestFilter;

class LAMAPOLL_RespondentImport
{
    public static function testImportFile($csvFile, $csvOptions): array
    {
        $csvOptions['useHeaderForLabels'] = false; // DISABLE HEADER LABELING FOR CHECK!

        $csvData = LAMA_CSVReader::read_csv($csvFile, $csvOptions, LP_IMPORT_PREVIEW_LINES);

        $errors = [];

        if ($csvOptions['useAttributes'] && $csvOptions['importAttributes'] != 'all') {
            if (
                in_array($csvOptions['nameColumn'], $csvOptions['importAttributes']) || in_array(
                    $csvOptions['emailColumn'],
                    $csvOptions['importAttributes']
                )
            ) {
                $errors[] = lp_get_text('IMPORT_ERR_ATTRIBUTE_USE');
            }
        }

        if (!self::checkValidEmailField($csvData, intval($csvOptions['emailColumn']) - 1)) {
            $errors[] = lp_get_text('IMPORT_ERR_NO_EMAIL');
        }

        return $errors;
    }

    private static function checkValidEmailField($csvData, $emailField)
    {
        $numEmails = $numInvalidEmails = 0;

        foreach ($csvData as $entry) {
            if (!isset($entry[$emailField])) {
                continue;
            }
            $numEmails++;
            $email = str_entity_decode($entry[$emailField]);
            if (!isEmail($email)) {
                $numInvalidEmails++;
            }
        }

        if ($numInvalidEmails > $numEmails / 2) {
            return false;
        } // Invalids more than 50%

        return true;
    }
}
