{$editable=$isBackend && $pollPolicy->canUpdateById($poll->getId())}

{$resultTextBefore=$resultView->getTextBefore()}
{$resultTextAfter=$resultView->getTextAfter()}

<div id="resultView_{$resultView->getId()}" class="resultView">{strip}
        {if $isBackend}
            {$placeholderText=$resultView->getUiText($poll,'TEXT_BEFORE', 'edit')}
			<div id="lp-edit-result-headline-{$resultView->getId()}" class="lp-ps-questionInfoText cf"
                    {if $editable}
				data-editable="true"
				data-params="poll={$poll->getId()}&page={$pageId}&item={$resultView->getId()}&src=text_before"
				data-placeholder="{$placeholderText|escape:'html'}"
				data-title="{lp_get_text('TOOLTIP_EDIT_RESULTVIEW_TEXT_BEFORE')}"
				data-plugin="pollPlaceholder"
			data-html="{$resultTextBefore|escape}"{/if}>
                {if !$editable}{$resultTextBefore}{/if}
			</div>
        {else}
			<div class="cf fr-view">
                {lp_parse_text($poll,$resultTextBefore)}
			</div>
        {/if}

        {if $isBackend}
			<input type="hidden" class="chartType" value="jqplot" />
        {/if}

        {if $resultView->getChartWidth()}{$chart_width=$resultView->getChartWidth()}{else}{$chart_width=$smarty.const.LP_DEFAULT_CHART_WIDTH}{/if}
        {if $resultView->getChartHeight()}{$chart_height=$resultView->getChartHeight()}{else}{$chart_height=$smarty.const.LP_DEFAULT_CHART_HEIGHT}{/if}
        {$chartMargin=4}
		<div style="display:block;background:#FFFFFF; border:1px solid grey;{if $resultView->getChartType()!='tab'}width:{$chart_width}px;padding:{$chartMargin}px;{else}width:{$smarty.const.LP_DEFAULT_CHART_WIDTH}px;{/if}">
			<div id="resultViewContent_{$resultView->getId()}" class="resultViewContent {if $isBackend}lp-item-holder lp-resultview-edit{/if}" {if $isBackend}title="{lp_get_text('TOOLTIP_EDIT_RESULTVIEW')}"{/if} style="{if $resultView->getChartType()!='tab'}width:{$chart_width}px;{/if}">
                {include file=$frontendTpl|cat:'poll/resultview/frontend.resultview.jqplot.tpl' plotID=$resultView->getId()}
			</div>
		</div>
        {if $isBackend}
            {$placeholderText=$resultView->getUiText($poll,'TEXT_AFTER', 'edit')}
			<div id="lp-edit-result-footline-{$resultView->getId()}" class="lp-ps-questionInfoText cf" {if $editable}
				data-editable="true"
				data-params="poll={$poll->getId()}&page={$pageId}&item={$resultView->getId()}&src=text_after"
				data-placeholder="{$placeholderText|escape:'html'}"
				data-title="{lp_get_text('TOOLTIP_EDIT_RESULTVIEW_TEXT_AFTER')}"{/if}
				 data-plugin="pollPlaceholder"
				 data-html="{$resultTextAfter|escape}">
			</div>
        {else}
			<div class="cf fr-view">
                {lp_parse_text($poll,$resultTextAfter)}
			</div>
        {/if}

    {/strip}
</div>