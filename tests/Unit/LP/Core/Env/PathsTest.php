<?php

declare(strict_types=1);

namespace <PERSON>no\Tests\Unit\LP\Core\Env;

use Lamano\Tests\Unit\LP\Core\LpCoreTestCase;
use Lamano\Tests\Utils\Traits\TestData;

class PathsTest extends LpCoreTestCase
{
    use TestData;

    protected static string $rootPath;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
    }

    /**
     * @covers \Lamano\LP\Core\Env\Paths
     */
    public function testPathsClass()
    {
        $paths = env()->config()->paths();

        $testAcc = $this->createTestAccount();

        // Test pathes
        self::assertTrue(is_dir($paths->privateAccountDataPath($testAcc->getAccountId())));
        self::assertTrue(is_dir($paths->publicAccountDataPath($testAcc->getAccountId())));
        self::assertTrue(is_dir($paths->legacyTemplatesPath()));

        // Test magic method
        $libPath = $paths->externalsPath();
        self::assertTrue(is_dir($libPath));

        // Test path creation
        $subdir = sprintf('%s/', self::$faker->md5());
        $tempPath = $paths->tempPath($subdir, false);
        if (is_dir($tempPath)) {
            rmdir($tempPath); // ensure directory doesn't exist
        }
        self::assertTrue(is_dir($paths->tempPath($subdir))); // this should always create a directory

        // Test path wihtout creating it
        rmdir($tempPath);
        self::assertFalse(is_dir($paths->tempPath($subdir, false))); // this should never create a directory
    }
}
