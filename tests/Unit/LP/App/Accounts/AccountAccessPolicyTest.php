<?php

/**
 * @noinspection PhpUnhandledExceptionInspection
 */

declare(strict_types=1);

namespace <PERSON><PERSON>\Tests\Unit\LP\App\Accounts;

use Lamano\LP\App\Accounts\Policy\AccountAccessPolicy;
use <PERSON>no\LP\App\Features\Features;
use <PERSON>no\LP\App\IdentityCard\IdentityCardGenerator;
use Lamano\LP\App\Orders\Models\Plan;
use Lamano\LP\App\Policies\PolicyService;
use Lamano\Tests\Utils\LpTestCase;
use Lamano\Tests\Utils\Traits\TestData;

/**
 * @coversDefaultClass  \Lamano\LP\App\Accounts\Policy\AccountAccessPolicy
 */
class AccountAccessPolicyTest extends LpTestCase
{
    use TestData;

    protected static PolicyService $policyService;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$policyService = di(PolicyService::class);
    }

    /**
     * @covers ::canList
     * @covers ::canCreate
     */
    public function test_FEATURE_SECURITY_MODULE_is_required()
    {
        $insufficientPlan = Plan::TARIF_FREE;
        $account = $this->createTestAccount($insufficientPlan);
        $mainUser = $account->getMainUser();
        $identityCard = IdentityCardGenerator::generate($mainUser);

        self::assertFalse($identityCard->hasFeature(Features::FEATURE_SECURITY_MODULE));

        /**
         * @var AccountAccessPolicy $policy
         */
        $policy = self::$policyService->getPolicyForUser(AccountAccessPolicy::class, $mainUser);
        self::assertFalse($policy->canList());
        self::assertFalse($policy->canCreate());
    }

    /**
     * @covers ::canList
     * @covers ::canCreate
     */
    public function test_mainUser()
    {
        $account = $this->createTestAccount(Plan::TARIF_ENTERPRISE_A);
        $mainUser = $account->getMainUser();

        /**
         * @var AccountAccessPolicy $policy
         */
        $policy = self::$policyService->getPolicyForUser(AccountAccessPolicy::class, $mainUser);
        self::assertTrue($policy->canList());
        self::assertTrue($policy->canCreate());
    }

    /**
     * @covers ::canList
     * @covers ::canCreate
     */
    public function test_subUser()
    {
        $account = $this->createTestAccount(Plan::TARIF_ENTERPRISE_A);
        $subUser = $this->createSubUser($account);

        /**
         * @var AccountAccessPolicy $policy
         */
        $policy = self::$policyService->getPolicyForUser(AccountAccessPolicy::class, $subUser);
        self::assertFalse($policy->canList());
        self::assertFalse($policy->canCreate());
    }

    /**
     * @covers ::canList
     * @covers ::canCreate
     */
    public function test_admin()
    {
        $account = $this->createTestAccount(Plan::TARIF_ENTERPRISE_A);
        $mainUser = $account->getMainUser();
        $admin = $this->createAdminUser();

        /**
         * @var AccountAccessPolicy $policy
         */
        $policy = self::$policyService->getPolicyForAdminAsUser(AccountAccessPolicy::class, $admin, $mainUser);
        self::assertTrue($policy->canList());
        self::assertFalse($policy->canCreate());
    }

    /**
     * @covers ::canRead
     * @covers ::canUpdate
     * @covers ::canDelete
     */
    public function test_not_implemented()
    {
        $account = $this->createTestAccount(Plan::TARIF_ENTERPRISE_A);
        $mainUser = $account->getMainUser();

        /**
         * @var AccountAccessPolicy $policy
         */
        $policy = self::$policyService->getPolicyForUser(AccountAccessPolicy::class, $mainUser);
        self::assertFalse($policy->canRead());
        self::assertFalse($policy->canUpdate());
        self::assertFalse($policy->canDelete());
    }
}
