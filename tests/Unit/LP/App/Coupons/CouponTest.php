<?php

declare(strict_types=1);

namespace Lamano\Tests\Unit\LP\App\Coupons;

use Lamano\LP\App\Coupons\CouponsService;
use Lamano\LP\App\Coupons\Models\TarifCoupon;
use Lamano\Tests\Utils\LpTestCase;

/**
 * @coversDefaultClass  \Lamano\LP\App\Coupons\CouponsService
 */
class CouponTest extends LpTestCase
{
    /**
     * @covers ::isCouponValid
     */
    public function test_couponUsage()
    {
        $coupon = TarifCoupon::factory()->createOne();
        $maxUsable = $coupon->getMaxUsable();
        $couponService = di(CouponsService::class);
        $i = 0;
        while ($i++ < $maxUsable) {
            $couponInDb = TarifCoupon::query()->requireOneByCouponId($coupon->getCouponId());
            $isValidCoupon = $couponService->isCouponValid(
                $couponInDb,
                $couponInDb->getValidForPlan(),
                $couponInDb->getValidPlanDuration()
            );
            self::assertTrue($isValidCoupon);

            $couponInDb->increaseUse()->save();
        }

        $couponInDb = TarifCoupon::query()->requireOneByCouponId($coupon->getCouponId());
        $isValidCoupon = $couponService->isCouponValid(
            $couponInDb,
            $couponInDb->getValidForPlan(),
            $couponInDb->getValidPlanDuration()
        );
        self::assertFalse($isValidCoupon);
    }
}
