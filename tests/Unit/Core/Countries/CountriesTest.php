<?php

declare(strict_types=1);

namespace <PERSON><PERSON>\Tests\Unit\Core\Countries;

use Lamano\LP\App\Orders\PriceCalculator;
use Lamano\Tests\Utils\LpTestCase;
use PHPUnit\Framework\Attributes\TestWith;

/**
 * @covers \Lamano\LP\Core\Countries\Countries
 */
class CountriesTest extends LpTestCase
{
    /**
     * @covers PriceCalculator::isTaxableCountry
     */
    #[TestWith(['de', true])]
    #[TestWith(['IT', true])]
    #[TestWith(['CH', false])]
    #[TestWith(['us', false])]
    public function testIsTaxEU(string $country, bool $expectedResult)
    {
        self::assertEquals($expectedResult, PriceCalculator::isTaxableCountry($country));
    }
}
