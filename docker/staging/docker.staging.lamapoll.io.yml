version: "3.7"

x-defaults:
    &defaults
    logging:
        driver: "json-file"
        options:
            max-size: "${LAMAPOLL_LOG_SIZE:-64m}"
            max-file: "${LAMAPOLL_LOG_FILES:-1}"
    extra_hosts:
      - "host.docker.internal:host-gateway"

services:
    lp-staging-app-php:
        <<: *defaults
        build:
          context: php/web
          args:
            ENV_TAG: "staging"
        image: lp-app-php:staging
        container_name: lp-staging-app-php
        hostname: lp-staging-app-php
        volumes:
            - "../../:/app"

    lp-staging-app-nginx:
        <<: *defaults
        build:
          context: nginx
          args:
            APP_CONFIG: "staging.lamapoll.io.conf"
        image: lp-app-nginx:staging
        container_name: lp-staging-app-nginx
        hostname: lp-staging-app-nginx
        depends_on:
            - lp-staging-app-php
        volumes:
            - "../../:/app"
        ports:
            - "${LAMAPOLL_APP_PORT:-8080}:8080"

    lp-staging-api-nginx:
        <<: *defaults
        build:
          context: nginx
          args:
            APP_CONFIG: "staging.lamapoll.io.conf"
        image: lp-api-nginx:staging
        container_name: lp-staging-api-nginx
        hostname: lp-staging-api-nginx
        depends_on:
            - lp-staging-app-php
        volumes:
            - "../../:/app"
        ports:
            - "${LAMAPOLL_API_PORT:-8081}:8080"

    lp-staging-survey-nginx:
        <<: *defaults
        build:
          context: nginx
          args:
            APP_CONFIG: "staging.lamapoll.io.conf"
        image: lp-survey-nginx:staging
        container_name: lp-staging-survey-nginx
        hostname: lp-staging-survey-nginx
        depends_on:
            - lp-staging-app-php
        volumes:
            - "../../:/app"
        ports:
            - "${LAMAPOLL_SURVEY_PORT:-8083}:8080"

    lp-staging-cron:
        <<: *defaults
        build:
          context: ./php/cron
          args:
            ENV_TAG: "staging"
        image: lp-app-cron:staging
        container_name: lp-staging-cron
        hostname: lp-staging-cron
        volumes:
            - "../../:/app"
            - "./php/cron/cron.d:/etc/crontabs"

    lp-staging-clamd:
        <<: *defaults
        image: clamav/clamav
        container_name: lp-staging-clamd
        hostname: lp-staging-clamd
        environment:
          - CLAMAV_NO_FRESHCLAMD=true
